const set = require('regenerate')(0xD7, 0xF7, 0x374, 0x37E, 0x385, 0x387, 0x605, 0x60C, 0x61B, 0x61F, 0x640, 0x6DD, 0x8E2, 0xE3F, 0x10FB, 0x1805, 0x1CD3, 0x1CE1, 0x1CFA, 0x3006, 0x30A0, 0x31EF, 0x32FF, 0xA92E, 0xA9CF, 0xAB5B, 0xFEFF, 0xFF70, 0x1D4A2, 0x1D4BB, 0x1D546, 0x1F7F0, 0xE0001);
set.addRange(0x0, 0x40).addRange(0x5B, 0x60).addRange(0x7B, 0xA9).addRange(0xAB, 0xB9).addRange(0xBB, 0xBF).addRange(0x2B9, 0x2DF).addRange(0x2E5, 0x2E9).addRange(0x2EC, 0x2FF).addRange(0x964, 0x965).addRange(0xFD5, 0xFD8).addRange(0x16EB, 0x16ED).addRange(0x1735, 0x1736).addRange(0x1802, 0x1803).addRange(0x1CE9, 0x1CEC).addRange(0x1CEE, 0x1CF3).addRange(0x1CF5, 0x1CF7).addRange(0x2000, 0x200B).addRange(0x200E, 0x2064).addRange(0x2066, 0x2070).addRange(0x2074, 0x207E).addRange(0x2080, 0x208E).addRange(0x20A0, 0x20C0).addRange(0x2100, 0x2125).addRange(0x2127, 0x2129).addRange(0x212C, 0x2131).addRange(0x2133, 0x214D).addRange(0x214F, 0x215F).addRange(0x2189, 0x218B).addRange(0x2190, 0x2429).addRange(0x2440, 0x244A).addRange(0x2460, 0x27FF).addRange(0x2900, 0x2B73).addRange(0x2B76, 0x2B95).addRange(0x2B97, 0x2BFF).addRange(0x2E00, 0x2E5D).addRange(0x2FF0, 0x3004).addRange(0x3008, 0x3020).addRange(0x3030, 0x3037).addRange(0x303C, 0x303F).addRange(0x309B, 0x309C).addRange(0x30FB, 0x30FC).addRange(0x3190, 0x319F).addRange(0x31C0, 0x31E5).addRange(0x3220, 0x325F).addRange(0x327F, 0x32CF).addRange(0x3358, 0x33FF).addRange(0x4DC0, 0x4DFF).addRange(0xA700, 0xA721).addRange(0xA788, 0xA78A).addRange(0xA830, 0xA839).addRange(0xAB6A, 0xAB6B);
set.addRange(0xFD3E, 0xFD3F).addRange(0xFE10, 0xFE19).addRange(0xFE30, 0xFE52).addRange(0xFE54, 0xFE66).addRange(0xFE68, 0xFE6B).addRange(0xFF01, 0xFF20).addRange(0xFF3B, 0xFF40).addRange(0xFF5B, 0xFF65).addRange(0xFF9E, 0xFF9F).addRange(0xFFE0, 0xFFE6).addRange(0xFFE8, 0xFFEE).addRange(0xFFF9, 0xFFFD).addRange(0x10100, 0x10102).addRange(0x10107, 0x10133).addRange(0x10137, 0x1013F).addRange(0x10190, 0x1019C).addRange(0x101D0, 0x101FC).addRange(0x102E1, 0x102FB).addRange(0x1BCA0, 0x1BCA3).addRange(0x1CC00, 0x1CCF9).addRange(0x1CD00, 0x1CEB3).addRange(0x1CF50, 0x1CFC3).addRange(0x1D000, 0x1D0F5).addRange(0x1D100, 0x1D126).addRange(0x1D129, 0x1D166).addRange(0x1D16A, 0x1D17A).addRange(0x1D183, 0x1D184).addRange(0x1D18C, 0x1D1A9).addRange(0x1D1AE, 0x1D1EA).addRange(0x1D2C0, 0x1D2D3).addRange(0x1D2E0, 0x1D2F3).addRange(0x1D300, 0x1D356).addRange(0x1D360, 0x1D378).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C).addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505).addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D7CB).addRange(0x1D7CE, 0x1D7FF);
set.addRange(0x1EC71, 0x1ECB4).addRange(0x1ED01, 0x1ED3D).addRange(0x1F000, 0x1F02B).addRange(0x1F030, 0x1F093).addRange(0x1F0A0, 0x1F0AE).addRange(0x1F0B1, 0x1F0BF).addRange(0x1F0C1, 0x1F0CF).addRange(0x1F0D1, 0x1F0F5).addRange(0x1F100, 0x1F1AD).addRange(0x1F1E6, 0x1F1FF).addRange(0x1F201, 0x1F202).addRange(0x1F210, 0x1F23B).addRange(0x1F240, 0x1F248).addRange(0x1F250, 0x1F251).addRange(0x1F260, 0x1F265).addRange(0x1F300, 0x1F6D7).addRange(0x1F6DC, 0x1F6EC).addRange(0x1F6F0, 0x1F6FC).addRange(0x1F700, 0x1F776).addRange(0x1F77B, 0x1F7D9).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F800, 0x1F80B).addRange(0x1F810, 0x1F847).addRange(0x1F850, 0x1F859).addRange(0x1F860, 0x1F887).addRange(0x1F890, 0x1F8AD).addRange(0x1F8B0, 0x1F8BB).addRange(0x1F8C0, 0x1F8C1).addRange(0x1F900, 0x1FA53).addRange(0x1FA60, 0x1FA6D).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8).addRange(0x1FB00, 0x1FB92).addRange(0x1FB94, 0x1FBF9).addRange(0xE0020, 0xE007F);
exports.characters = set;
