{"ast": null, "code": "import api from './api';\nconst paymentService = {\n  // Create payment intent\n  createPaymentIntent: async (orderData, token) => {\n    const response = await api.post('/payment/create-intent', orderData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Confirm payment\n  confirmPayment: async (paymentIntentId, token) => {\n    const response = await api.post('/payment/confirm', {\n      paymentIntentId\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Get payment methods\n  getPaymentMethods: async token => {\n    const response = await api.get('/payment/methods', {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Add payment method\n  addPaymentMethod: async (paymentMethodData, token) => {\n    const response = await api.post('/payment/methods', paymentMethodData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Delete payment method\n  deletePaymentMethod: async (paymentMethodId, token) => {\n    const response = await api.delete(`/payment/methods/${paymentMethodId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  }\n};\nexport default paymentService;", "map": {"version": 3, "names": ["api", "paymentService", "createPaymentIntent", "orderData", "token", "response", "post", "headers", "Authorization", "data", "confirmPayment", "paymentIntentId", "getPaymentMethods", "get", "addPaymentMethod", "paymentMethodData", "deletePaymentMethod", "paymentMethodId", "delete"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/paymentService.js"], "sourcesContent": ["import api from './api';\n\nconst paymentService = {\n  // Create payment intent\n  createPaymentIntent: async (orderData, token) => {\n    const response = await api.post('/payment/create-intent', orderData, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Confirm payment\n  confirmPayment: async (paymentIntentId, token) => {\n    const response = await api.post('/payment/confirm', \n      { paymentIntentId }, \n      {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      }\n    );\n    return response.data;\n  },\n\n  // Get payment methods\n  getPaymentMethods: async (token) => {\n    const response = await api.get('/payment/methods', {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Add payment method\n  addPaymentMethod: async (paymentMethodData, token) => {\n    const response = await api.post('/payment/methods', paymentMethodData, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Delete payment method\n  deletePaymentMethod: async (paymentMethodId, token) => {\n    const response = await api.delete(`/payment/methods/${paymentMethodId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n};\n\nexport default paymentService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,cAAc,GAAG;EACrB;EACAC,mBAAmB,EAAE,MAAAA,CAAOC,SAAS,EAAEC,KAAK,KAAK;IAC/C,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,wBAAwB,EAAEH,SAAS,EAAE;MACnEI,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUJ,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;EACAC,cAAc,EAAE,MAAAA,CAAOC,eAAe,EAAEP,KAAK,KAAK;IAChD,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,kBAAkB,EAChD;MAAEK;IAAgB,CAAC,EACnB;MACEJ,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUJ,KAAK;MAChC;IACF,CACF,CAAC;IACD,OAAOC,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;EACAG,iBAAiB,EAAE,MAAOR,KAAK,IAAK;IAClC,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACa,GAAG,CAAC,kBAAkB,EAAE;MACjDN,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUJ,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;EACAK,gBAAgB,EAAE,MAAAA,CAAOC,iBAAiB,EAAEX,KAAK,KAAK;IACpD,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACM,IAAI,CAAC,kBAAkB,EAAES,iBAAiB,EAAE;MACrER,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUJ,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACI,IAAI;EACtB,CAAC;EAED;EACAO,mBAAmB,EAAE,MAAAA,CAAOC,eAAe,EAAEb,KAAK,KAAK;IACrD,MAAMC,QAAQ,GAAG,MAAML,GAAG,CAACkB,MAAM,CAAC,oBAAoBD,eAAe,EAAE,EAAE;MACvEV,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUJ,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOC,QAAQ,CAACI,IAAI;EACtB;AACF,CAAC;AAED,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}