const express = require('express');
const {
  register,
  login,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification
} = require('../controllers/authController');

const { protect, createRateLimit } = require('../middleware/auth');
const {
  validateUserRegistration,
  validateUserLogin,
  validateUserUpdate,
  validateObjectId
} = require('../middleware/validation');

const router = express.Router();

// Rate limiting for auth routes (relaxed for development)
const authRateLimit = createRateLimit(15 * 60 * 1000, 50); // 50 requests per 15 minutes (development)
const passwordRateLimit = createRateLimit(60 * 60 * 1000, 10); // 10 requests per hour (development)

// Public routes
router.post('/register', validateUserRegistration, register);
router.post('/login', authRateLimit, validateUserLogin, login);
router.post('/forgot-password', passwordRateLimit, forgotPassword);
router.post('/reset-password/:token', passwordRateLimit, resetPassword);
router.post('/verify-email/:token', verifyEmail);

// Protected routes
router.use(protect); // All routes below require authentication

router.get('/profile', getProfile);
router.put('/profile', validateUserUpdate, updateProfile);
router.put('/change-password', changePassword);
router.post('/resend-verification', resendVerification);

module.exports = router;
