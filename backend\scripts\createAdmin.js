const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grocery-store')
  .then(() => console.log('✅ MongoDB connected for admin creation'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// Admin user data
const adminData = {
  firstName: 'Admin',
  lastName: 'User',
  email: '<EMAIL>',
  password: 'admin123',
  role: 'admin',
  isActive: true
};

async function createAdmin() {
  try {
    // Check if admin already exists and delete it
    const existingAdmin = await User.findOne({ email: adminData.email });

    if (existingAdmin) {
      await User.deleteOne({ email: adminData.email });
      console.log('🗑️ Existing admin user deleted');
    }

    // Create admin user using User.create (this will trigger pre-save middleware)
    const admin = await User.create(adminData);
    
    console.log('✅ Admin user created successfully!');
    console.log('📧 Email:', adminData.email);
    console.log('🔑 Password:', adminData.password);
    console.log('👑 Role: admin');
    
  } catch (error) {
    console.error('❌ Error creating admin:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the admin creation
if (require.main === module) {
  createAdmin();
}

module.exports = { createAdmin };
