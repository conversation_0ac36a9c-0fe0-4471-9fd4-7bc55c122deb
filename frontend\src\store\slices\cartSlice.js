import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import cartService from '../../services/cartService';

const initialState = {
  items: [],
  totalItems: 0,
  totalPrice: 0,
  discountAmount: 0,
  finalPrice: 0,
  appliedCoupon: null,
  isLoading: false,
  isError: false,
  message: '',
};

// Get cart
export const getCart = createAsyncThunk(
  'cart/getCart',
  async (_, thunkAPI) => {
    try {
      return await cartService.getCart();
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Add item to cart
export const addToCart = createAsyncThunk(
  'cart/addItem',
  async ({ productId, quantity }, thunkAPI) => {
    try {
      return await cartService.addToCart(productId, quantity);
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Update cart item
export const updateCartItem = createAsyncThunk(
  'cart/updateItem',
  async ({ productId, quantity }, thunkAPI) => {
    try {
      return await cartService.updateCartItem(productId, quantity);
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Remove item from cart
export const removeFromCart = createAsyncThunk(
  'cart/removeItem',
  async (productId, thunkAPI) => {
    try {
      return await cartService.removeFromCart(productId);
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Clear cart
export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (_, thunkAPI) => {
    try {
      return await cartService.clearCart();
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Apply coupon
export const applyCoupon = createAsyncThunk(
  'cart/applyCoupon',
  async (couponCode, thunkAPI) => {
    try {
      return await cartService.applyCoupon(couponCode);
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Remove coupon
export const removeCoupon = createAsyncThunk(
  'cart/removeCoupon',
  async (_, thunkAPI) => {
    try {
      return await cartService.removeCoupon();
    } catch (error) {
      const message = error.response?.data?.message || error.message || error.toString();
      return thunkAPI.rejectWithValue(message);
    }
  }
);

export const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    reset: (state) => {
      state.isLoading = false;
      state.isError = false;
      state.message = '';
    },
    clearError: (state) => {
      state.isError = false;
      state.message = '';
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(getCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getCart.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        const items = action.payload.items || [];
        state.items = items;
        state.totalItems = cart.totalItems;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(getCart.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(addToCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        const items = action.payload.items || [];
        state.items = items;
        state.totalItems = cart.totalItems;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(updateCartItem.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(updateCartItem.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        const items = action.payload.items || [];
        state.items = items;
        state.totalItems = cart.totalItems;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(updateCartItem.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(removeFromCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(removeFromCart.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        const items = action.payload.items || [];
        state.items = items;
        state.totalItems = cart.totalItems;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(removeFromCart.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(clearCart.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.isLoading = false;
        state.items = [];
        state.totalItems = 0;
        state.totalPrice = 0;
        state.discountAmount = 0;
        state.finalPrice = 0;
        state.appliedCoupon = null;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(applyCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(applyCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(applyCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      })
      .addCase(removeCoupon.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(removeCoupon.fulfilled, (state, action) => {
        state.isLoading = false;
        const cart = action.payload.cart;
        state.totalPrice = cart.totalPrice;
        state.discountAmount = cart.discountAmount;
        state.finalPrice = cart.finalPrice;
        state.appliedCoupon = cart.appliedCoupon;
      })
      .addCase(removeCoupon.rejected, (state, action) => {
        state.isLoading = false;
        state.isError = true;
        state.message = action.payload;
      });
  },
});

export const { reset, clearError } = cartSlice.actions;
export default cartSlice.reducer;
