import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getCart, addToCart } from '../../store/slices/cartSlice';

const CartDebug = () => {
  const dispatch = useDispatch();
  const cart = useSelector(state => state.cart);
  const auth = useSelector(state => state.auth);

  const handleTestAddToCart = async () => {
    try {
      console.log('Testing add to cart...');
      console.log('LocalStorage token:', localStorage.getItem('token'));
      console.log('Redux auth state:', auth);

      const result = await dispatch(addToCart({
        productId: '688c5d7adb720a4c64937040', // Test product ID
        quantity: 1
      })).unwrap();
      console.log('Add to cart result:', result);
    } catch (error) {
      console.error('Add to cart error:', error);
      console.error('Error details:', error.response?.data || error.message);
    }
  };

  const handleTestGetCart = async () => {
    try {
      console.log('Testing get cart...');
      const result = await dispatch(getCart()).unwrap();
      console.log('Get cart result:', result);
    } catch (error) {
      console.error('Get cart error:', error);
    }
  };

  return (
    <div className="p-4 bg-gray-100 border rounded">
      <h3 className="text-lg font-bold mb-4">Cart Debug Panel</h3>
      
      <div className="mb-4">
        <h4 className="font-semibold">Auth State:</h4>
        <pre className="text-xs bg-white p-2 rounded">
          {JSON.stringify({
            user: auth.user?.email,
            token: auth.token ? 'Present' : 'Missing',
            localStorageToken: localStorage.getItem('token') ? 'Present' : 'Missing',
            isLoggedIn: !!auth.user
          }, null, 2)}
        </pre>
      </div>

      <div className="mb-4">
        <h4 className="font-semibold">Cart State:</h4>
        <pre className="text-xs bg-white p-2 rounded">
          {JSON.stringify({
            totalItems: cart.totalItems,
            itemsCount: cart.items?.length || 0,
            isLoading: cart.isLoading,
            isError: cart.isError,
            message: cart.message
          }, null, 2)}
        </pre>
      </div>

      <div className="space-x-2">
        <button 
          onClick={handleTestGetCart}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Test Get Cart
        </button>
        <button 
          onClick={handleTestAddToCart}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Test Add to Cart
        </button>
      </div>

      {cart.items && cart.items.length > 0 && (
        <div className="mt-4">
          <h4 className="font-semibold">Cart Items:</h4>
          <pre className="text-xs bg-white p-2 rounded">
            {JSON.stringify(cart.items, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
};

export default CartDebug;
