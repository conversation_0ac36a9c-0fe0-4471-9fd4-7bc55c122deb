{"ast": null, "code": "import api from './api';\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Update cart item\n  updateCartItem: async (productId, quantity, token) => {\n    const response = await api.put('/cart/update', {\n      productId,\n      quantity\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Remove item from cart\n  removeFromCart: async (productId, token) => {\n    const response = await api.delete(`/cart/remove/${productId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Clear cart\n  clearCart: async token => {\n    const response = await api.delete('/cart/clear', {\n      headers: {\n        Authorization: `Bear<PERSON> ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Apply coupon\n  applyCoupon: async (couponCode, token) => {\n    const response = await api.post('/cart/coupon/apply', {\n      couponCode\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Remove coupon\n  removeCoupon: async token => {\n    const response = await api.delete('/cart/coupon/remove', {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Validate cart\n  validateCart: async token => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  }\n};\nexport default cartService;", "map": {"version": 3, "names": ["api", "cartService", "getCart", "response", "get", "data", "addToCart", "productId", "quantity", "post", "updateCartItem", "token", "put", "headers", "Authorization", "removeFromCart", "delete", "clearCart", "applyCoupon", "couponCode", "removeCoupon", "validateCart"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/cartService.js"], "sourcesContent": ["import api from './api';\n\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', { productId, quantity });\n    return response.data;\n  },\n\n  // Update cart item\n  updateCartItem: async (productId, quantity, token) => {\n    const response = await api.put('/cart/update', \n      { productId, quantity }, \n      {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      }\n    );\n    return response.data;\n  },\n\n  // Remove item from cart\n  removeFromCart: async (productId, token) => {\n    const response = await api.delete(`/cart/remove/${productId}`, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Clear cart\n  clearCart: async (token) => {\n    const response = await api.delete('/cart/clear', {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Apply coupon\n  applyCoupon: async (couponCode, token) => {\n    const response = await api.post('/cart/coupon/apply', \n      { couponCode }, \n      {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      }\n    );\n    return response.data;\n  },\n\n  // Remove coupon\n  removeCoupon: async (token) => {\n    const response = await api.delete('/cart/coupon/remove', {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Validate cart\n  validateCart: async (token) => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n};\n\nexport default cartService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,OAAO,CAAC;IACvC,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,KAAK;IACxC,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,WAAW,EAAE;MAAEF,SAAS;MAAEC;IAAS,CAAC,CAAC;IACrE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAK,cAAc,EAAE,MAAAA,CAAOH,SAAS,EAAEC,QAAQ,EAAEG,KAAK,KAAK;IACpD,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACY,GAAG,CAAC,cAAc,EAC3C;MAAEL,SAAS;MAAEC;IAAS,CAAC,EACvB;MACEK,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CACF,CAAC;IACD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,cAAc,EAAE,MAAAA,CAAOR,SAAS,EAAEI,KAAK,KAAK;IAC1C,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACgB,MAAM,CAAC,gBAAgBT,SAAS,EAAE,EAAE;MAC7DM,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,SAAS,EAAE,MAAON,KAAK,IAAK;IAC1B,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACgB,MAAM,CAAC,aAAa,EAAE;MAC/CH,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,WAAW,EAAE,MAAAA,CAAOC,UAAU,EAAER,KAAK,KAAK;IACxC,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,oBAAoB,EAClD;MAAEU;IAAW,CAAC,EACd;MACEN,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CACF,CAAC;IACD,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAe,YAAY,EAAE,MAAOT,KAAK,IAAK;IAC7B,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACgB,MAAM,CAAC,qBAAqB,EAAE;MACvDH,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,YAAY,EAAE,MAAOV,KAAK,IAAK;IAC7B,MAAMR,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE;MACpDI,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOR,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}