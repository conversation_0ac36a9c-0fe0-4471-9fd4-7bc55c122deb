/*!
 2022 <PERSON> <<EMAIL>>
 @version 8.0.7
*/
const i=/^(b|B)$/,t={iec:{bits:["bit","Kibit","Mibit","Gibit","Tibit","Pibit","Eibit","<PERSON>ibit","Yibit"],bytes:["B","Ki<PERSON>","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},jedec:{bits:["bit","Kbit","Mbit","Gbit","Tbit","Pbit","Ebit","Zbit","Ybit"],bytes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]}},e={iec:["","kibi","mebi","gibi","tebi","pebi","exbi","zebi","yobi"],jedec:["","kilo","mega","giga","tera","peta","exa","zetta","yotta"]},o={floor:Math.floor,ceil:Math.ceil};function a(a,r={}){let b,n,s,l,c,p,d,B,u,f,g,h,M,y,m,x,v,N,E,j,w,P=[],T=0;if(isNaN(a))throw new TypeError("Invalid number");if(s=!0===r.bits,m=!0===r.unix,h=!0===r.pad,n=r.base||10,M=void 0!==r.round?r.round:m?1:2,d=void 0!==r.locale?r.locale:"",B=r.localeOptions||{},x=void 0!==r.separator?r.separator:"",v=void 0!==r.spacer?r.spacer:m?"":" ",E=r.symbols||{},N=2===n?r.standard||"iec":"jedec",g=r.output||"string",c=!0===r.fullform,p=r.fullforms instanceof Array?r.fullforms:[],b=void 0!==r.exponent?r.exponent:-1,j=o[r.roundingMethod]||Math.round,f=Number(a),u=f<0,l=n>2?1e3:1024,w=!1===isNaN(r.precision)?parseInt(r.precision,10):0,u&&(f=-f),(-1===b||isNaN(b))&&(b=Math.floor(Math.log(f)/Math.log(l)),b<0&&(b=0)),b>8&&(w>0&&(w+=8-b),b=8),"exponent"===g)return b;if(0===f)P[0]=0,y=P[1]=m?"":t[N][s?"bits":"bytes"][b];else{T=f/(2===n?Math.pow(2,10*b):Math.pow(1e3,b)),s&&(T*=8,T>=l&&b<8&&(T/=l,b++));const e=Math.pow(10,b>0?M:0);P[0]=j(T*e)/e,P[0]===l&&b<8&&void 0===r.exponent&&(P[0]=1,b++),y=P[1]=10===n&&1===b?s?"kbit":"kB":t[N][s?"bits":"bytes"][b],m&&(P[1]=P[1].charAt(0),i.test(P[1])&&(P[0]=Math.floor(P[0]),P[1]=""))}if(u&&(P[0]=-P[0]),w>0&&(P[0]=P[0].toPrecision(w)),P[1]=E[P[1]]||P[1],!0===d?P[0]=P[0].toLocaleString():d.length>0?P[0]=P[0].toLocaleString(d,B):x.length>0&&(P[0]=P[0].toString().replace(".",x)),h&&!1===Number.isInteger(P[0])&&M>0){const i=x||".",t=P[0].toString().split(i),e=t[1]||"",o=e.length,a=M-o;P[0]=`${t[0]}${i}${e.padEnd(o+a,"0")}`}return c&&(P[1]=p[b]?p[b]:e[N][b]+(s?"bit":"byte")+(1===P[0]?"":"s")),"array"===g?P:"object"===g?{value:P[0],symbol:P[1],exponent:b,unit:y}:P.join(v)}a.partial=i=>t=>a(t,i);export{a as default};
//# sourceMappingURL=filesize.esm.min.js.map
