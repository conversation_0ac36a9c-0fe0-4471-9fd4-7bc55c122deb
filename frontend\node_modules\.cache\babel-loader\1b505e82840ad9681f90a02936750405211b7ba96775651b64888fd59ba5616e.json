{"ast": null, "code": "// Currency formatting utilities\n\n/**\n * Format price in Indian Rupees\n * @param {number} amount - The amount to format\n * @param {boolean} showSymbol - Whether to show the ₹ symbol\n * @returns {string} Formatted price string\n */\nexport const formatPrice = (amount, showSymbol = true) => {\n  if (typeof amount !== 'number' || isNaN(amount)) {\n    return showSymbol ? '₹0' : '0';\n  }\n\n  // Format number with Indian number system (lakhs, crores)\n  const formatter = new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2\n  });\n  if (showSymbol) {\n    return formatter.format(amount);\n  } else {\n    return formatter.format(amount).replace('₹', '').trim();\n  }\n};\n\n/**\n * Format price without currency symbol\n * @param {number} amount - The amount to format\n * @returns {string} Formatted price string without symbol\n */\nexport const formatPriceNumber = amount => {\n  return formatPrice(amount, false);\n};\n\n/**\n * Calculate discount percentage\n * @param {number} originalPrice - Original price\n * @param {number} currentPrice - Current price\n * @returns {number} Discount percentage\n */\nexport const calculateDiscountPercentage = (originalPrice, currentPrice) => {\n  if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {\n    return 0;\n  }\n  return Math.round((originalPrice - currentPrice) / originalPrice * 100);\n};\n\n/**\n * Format discount amount\n * @param {number} originalPrice - Original price\n * @param {number} currentPrice - Current price\n * @returns {string} Formatted discount amount\n */\nexport const formatDiscountAmount = (originalPrice, currentPrice) => {\n  const discount = originalPrice - currentPrice;\n  return discount > 0 ? formatPrice(discount) : '₹0';\n};", "map": {"version": 3, "names": ["formatPrice", "amount", "showSymbol", "isNaN", "formatter", "Intl", "NumberFormat", "style", "currency", "minimumFractionDigits", "maximumFractionDigits", "format", "replace", "trim", "formatPriceNumber", "calculateDiscountPercentage", "originalPrice", "currentPrice", "Math", "round", "formatDiscountAmount", "discount"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/utils/currency.js"], "sourcesContent": ["// Currency formatting utilities\n\n/**\n * Format price in Indian Rupees\n * @param {number} amount - The amount to format\n * @param {boolean} showSymbol - Whether to show the ₹ symbol\n * @returns {string} Formatted price string\n */\nexport const formatPrice = (amount, showSymbol = true) => {\n  if (typeof amount !== 'number' || isNaN(amount)) {\n    return showSymbol ? '₹0' : '0';\n  }\n\n  // Format number with Indian number system (lakhs, crores)\n  const formatter = new Intl.NumberFormat('en-IN', {\n    style: 'currency',\n    currency: 'INR',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 2,\n  });\n\n  if (showSymbol) {\n    return formatter.format(amount);\n  } else {\n    return formatter.format(amount).replace('₹', '').trim();\n  }\n};\n\n/**\n * Format price without currency symbol\n * @param {number} amount - The amount to format\n * @returns {string} Formatted price string without symbol\n */\nexport const formatPriceNumber = (amount) => {\n  return formatPrice(amount, false);\n};\n\n/**\n * Calculate discount percentage\n * @param {number} originalPrice - Original price\n * @param {number} currentPrice - Current price\n * @returns {number} Discount percentage\n */\nexport const calculateDiscountPercentage = (originalPrice, currentPrice) => {\n  if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {\n    return 0;\n  }\n  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);\n};\n\n/**\n * Format discount amount\n * @param {number} originalPrice - Original price\n * @param {number} currentPrice - Current price\n * @returns {string} Formatted discount amount\n */\nexport const formatDiscountAmount = (originalPrice, currentPrice) => {\n  const discount = originalPrice - currentPrice;\n  return discount > 0 ? formatPrice(discount) : '₹0';\n};\n"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,GAAGA,CAACC,MAAM,EAAEC,UAAU,GAAG,IAAI,KAAK;EACxD,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAIE,KAAK,CAACF,MAAM,CAAC,EAAE;IAC/C,OAAOC,UAAU,GAAG,IAAI,GAAG,GAAG;EAChC;;EAEA;EACA,MAAME,SAAS,GAAG,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;IAC/CC,KAAK,EAAE,UAAU;IACjBC,QAAQ,EAAE,KAAK;IACfC,qBAAqB,EAAE,CAAC;IACxBC,qBAAqB,EAAE;EACzB,CAAC,CAAC;EAEF,IAAIR,UAAU,EAAE;IACd,OAAOE,SAAS,CAACO,MAAM,CAACV,MAAM,CAAC;EACjC,CAAC,MAAM;IACL,OAAOG,SAAS,CAACO,MAAM,CAACV,MAAM,CAAC,CAACW,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC;EACzD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAIb,MAAM,IAAK;EAC3C,OAAOD,WAAW,CAACC,MAAM,EAAE,KAAK,CAAC;AACnC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,2BAA2B,GAAGA,CAACC,aAAa,EAAEC,YAAY,KAAK;EAC1E,IAAI,CAACD,aAAa,IAAI,CAACC,YAAY,IAAID,aAAa,IAAIC,YAAY,EAAE;IACpE,OAAO,CAAC;EACV;EACA,OAAOC,IAAI,CAACC,KAAK,CAAE,CAACH,aAAa,GAAGC,YAAY,IAAID,aAAa,GAAI,GAAG,CAAC;AAC3E,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,oBAAoB,GAAGA,CAACJ,aAAa,EAAEC,YAAY,KAAK;EACnE,MAAMI,QAAQ,GAAGL,aAAa,GAAGC,YAAY;EAC7C,OAAOI,QAAQ,GAAG,CAAC,GAAGrB,WAAW,CAACqB,QAAQ,CAAC,GAAG,IAAI;AACpD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}