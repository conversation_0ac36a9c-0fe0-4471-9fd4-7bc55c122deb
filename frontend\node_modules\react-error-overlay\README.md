# `react-error-overlay`

`react-error-overlay` is an overlay which displays when there is a runtime error.

## Development

When developing within this package, make sure you run `npm start` (or `yarn start`) so that the files are compiled as you work.
This is run in watch mode by default.

If you would like to build this for production, run `npm run build:prod` (or `yarn build:prod`).<br>
If you would like to build this one-off for development, you can run `NODE_ENV=development npm run build` (or `NODE_ENV=development yarn build`).
