{"ast": null, "code": "import api from './api';\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    console.log('Cart service: Adding to cart', {\n      productId,\n      quantity\n    });\n    console.log('Cart service: Token in localStorage', localStorage.getItem('token'));\n    const response = await api.post('/cart/add', {\n      productId,\n      quantity\n    });\n    console.log('Cart service: Add to cart response', response.data);\n    return response.data;\n  },\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Remove item from cart\n  removeFromCart: async productId => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n  // Apply coupon\n  applyCoupon: async couponCode => {\n    const response = await api.post('/cart/coupon/apply', {\n      couponCode\n    });\n    return response.data;\n  },\n  // Remove coupon\n  removeCoupon: async () => {\n    const response = await api.delete('/cart/coupon/remove');\n    return response.data;\n  },\n  // Validate cart\n  validateCart: async token => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  }\n};\nexport default cartService;", "map": {"version": 3, "names": ["api", "cartService", "getCart", "response", "get", "data", "addToCart", "productId", "quantity", "console", "log", "localStorage", "getItem", "post", "updateCartItem", "put", "removeFromCart", "delete", "clearCart", "applyCoupon", "couponCode", "removeCoupon", "validateCart", "token", "headers", "Authorization"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/cartService.js"], "sourcesContent": ["import api from './api';\n\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    console.log('Cart service: Adding to cart', { productId, quantity });\n    console.log('Cart service: Token in localStorage', localStorage.getItem('token'));\n    const response = await api.post('/cart/add', { productId, quantity });\n    console.log('Cart service: Add to cart response', response.data);\n    return response.data;\n  },\n\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', { productId, quantity });\n    return response.data;\n  },\n\n  // Remove item from cart\n  removeFromCart: async (productId) => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n\n  // Apply coupon\n  applyCoupon: async (couponCode) => {\n    const response = await api.post('/cart/coupon/apply', { couponCode });\n    return response.data;\n  },\n\n  // Remove coupon\n  removeCoupon: async () => {\n    const response = await api.delete('/cart/coupon/remove');\n    return response.data;\n  },\n\n  // Validate cart\n  validateCart: async (token) => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n};\n\nexport default cartService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,OAAO,CAAC;IACvC,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,KAAK;IACxCC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAAEH,SAAS;MAAEC;IAAS,CAAC,CAAC;IACpEC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;IACjF,MAAMT,QAAQ,GAAG,MAAMH,GAAG,CAACa,IAAI,CAAC,WAAW,EAAE;MAAEN,SAAS;MAAEC;IAAS,CAAC,CAAC;IACrEC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEP,QAAQ,CAACE,IAAI,CAAC;IAChE,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,cAAc,EAAE,MAAAA,CAAOP,SAAS,EAAEC,QAAQ,KAAK;IAC7C,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACe,GAAG,CAAC,cAAc,EAAE;MAAER,SAAS;MAAEC;IAAS,CAAC,CAAC;IACvE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAW,cAAc,EAAE,MAAOT,SAAS,IAAK;IACnC,MAAMJ,QAAQ,GAAG,MAAMH,GAAG,CAACiB,MAAM,CAAC,gBAAgBV,SAAS,EAAE,CAAC;IAC9D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAMf,QAAQ,GAAG,MAAMH,GAAG,CAACiB,MAAM,CAAC,aAAa,CAAC;IAChD,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAc,WAAW,EAAE,MAAOC,UAAU,IAAK;IACjC,MAAMjB,QAAQ,GAAG,MAAMH,GAAG,CAACa,IAAI,CAAC,oBAAoB,EAAE;MAAEO;IAAW,CAAC,CAAC;IACrE,OAAOjB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMlB,QAAQ,GAAG,MAAMH,GAAG,CAACiB,MAAM,CAAC,qBAAqB,CAAC;IACxD,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAiB,YAAY,EAAE,MAAOC,KAAK,IAAK;IAC7B,MAAMpB,QAAQ,GAAG,MAAMH,GAAG,CAACa,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE;MACpDW,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUF,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOpB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}