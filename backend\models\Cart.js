const mongoose = require('mongoose');

const cartItemSchema = new mongoose.Schema({
  product: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  quantity: {
    type: Number,
    required: true,
    min: [1, 'Quantity must be at least 1'],
    default: 1
  },
  price: {
    type: Number,
    required: true,
    min: [0, 'Price cannot be negative']
  },
  addedAt: {
    type: Date,
    default: Date.now
  }
});

const cartSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  },
  items: [cartItemSchema],
  totalItems: {
    type: Number,
    default: 0
  },
  totalPrice: {
    type: Number,
    default: 0
  },
  appliedCoupons: [{
    code: {
      type: String,
      required: true
    },
    discount: {
      type: Number,
      required: true
    },
    discountType: {
      type: String,
      enum: ['percentage', 'fixed'],
      required: true
    }
  }],
  discountAmount: {
    type: Number,
    default: 0
  },
  finalPrice: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    index: { expireAfterSeconds: 0 }
  }
}, {
  timestamps: true
});

// Indexes
cartSchema.index({ user: 1 });
cartSchema.index({ 'items.product': 1 });
cartSchema.index({ expiresAt: 1 });

// Pre-save middleware to calculate totals
cartSchema.pre('save', function(next) {
  // Calculate total items and total price
  this.totalItems = this.items.reduce((total, item) => total + item.quantity, 0);
  this.totalPrice = this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  
  // Calculate discount amount
  this.discountAmount = this.appliedCoupons.reduce((total, coupon) => {
    if (coupon.discountType === 'percentage') {
      return total + (this.totalPrice * coupon.discount / 100);
    } else {
      return total + coupon.discount;
    }
  }, 0);
  
  // Calculate final price
  this.finalPrice = Math.max(0, this.totalPrice - this.discountAmount);
  
  next();
});

// Instance method to add item to cart
cartSchema.methods.addItem = async function(productId, quantity = 1, price) {
  const existingItemIndex = this.items.findIndex(
    item => item.product.toString() === productId.toString()
  );
  
  if (existingItemIndex > -1) {
    // Update existing item
    this.items[existingItemIndex].quantity += quantity;
    this.items[existingItemIndex].addedAt = new Date();
  } else {
    // Add new item
    this.items.push({
      product: productId,
      quantity,
      price,
      addedAt: new Date()
    });
  }
  
  return await this.save();
};

// Instance method to update item quantity
cartSchema.methods.updateItemQuantity = async function(productId, quantity) {
  const itemIndex = this.items.findIndex(
    item => item.product.toString() === productId.toString()
  );
  
  if (itemIndex === -1) {
    throw new Error('Item not found in cart');
  }
  
  if (quantity <= 0) {
    // Remove item if quantity is 0 or negative
    this.items.splice(itemIndex, 1);
  } else {
    this.items[itemIndex].quantity = quantity;
  }
  
  return await this.save();
};

// Instance method to remove item from cart
cartSchema.methods.removeItem = async function(productId) {
  this.items = this.items.filter(
    item => item.product.toString() !== productId.toString()
  );
  
  return await this.save();
};

// Instance method to clear cart
cartSchema.methods.clearCart = async function() {
  this.items = [];
  this.appliedCoupons = [];
  return await this.save();
};

// Instance method to apply coupon
cartSchema.methods.applyCoupon = async function(couponCode, discount, discountType) {
  // Check if coupon is already applied
  const existingCoupon = this.appliedCoupons.find(coupon => coupon.code === couponCode);
  if (existingCoupon) {
    throw new Error('Coupon already applied');
  }
  
  this.appliedCoupons.push({
    code: couponCode,
    discount,
    discountType
  });
  
  return await this.save();
};

// Instance method to remove coupon
cartSchema.methods.removeCoupon = async function(couponCode) {
  this.appliedCoupons = this.appliedCoupons.filter(coupon => coupon.code !== couponCode);
  return await this.save();
};

// Static method to find or create cart for user
cartSchema.statics.findOrCreateCart = async function(userId) {
  let cart = await this.findOne({ user: userId, isActive: true })
    .populate('items.product', 'name price images stock.quantity isActive');
  
  if (!cart) {
    cart = new this({ user: userId });
    await cart.save();
  }
  
  return cart;
};

// Static method to get cart with populated product details
cartSchema.statics.getCartWithProducts = async function(userId) {
  return await this.findOne({ user: userId, isActive: true })
    .populate({
      path: 'items.product',
      select: 'name price images stock.quantity isActive category brand',
      populate: {
        path: 'category',
        select: 'name'
      }
    });
};

// Instance method to validate cart items (check stock, active products)
cartSchema.methods.validateItems = async function() {
  const invalidItems = [];
  const validItems = [];
  
  for (const item of this.items) {
    if (!item.product.isActive) {
      invalidItems.push({
        item,
        reason: 'Product is no longer available'
      });
    } else if (item.product.stock.trackInventory && item.product.stock.quantity < item.quantity) {
      invalidItems.push({
        item,
        reason: `Only ${item.product.stock.quantity} items available in stock`
      });
    } else {
      validItems.push(item);
    }
  }
  
  return { validItems, invalidItems };
};

// Instance method to get cart summary
cartSchema.methods.getSummary = function() {
  return {
    totalItems: this.totalItems,
    totalPrice: this.totalPrice,
    discountAmount: this.discountAmount,
    finalPrice: this.finalPrice,
    appliedCoupons: this.appliedCoupons,
    itemCount: this.items.length
  };
};

module.exports = mongoose.model('Cart', cartSchema);
