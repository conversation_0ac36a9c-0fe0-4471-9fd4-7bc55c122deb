@echo off
echo ========================================
echo    BAHUCHAR GROCERIES STORE SETUP
echo ========================================
echo.

echo [1/6] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo ✓ Node.js is installed

echo.
echo [2/6] Installing backend dependencies...
cd backend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install backend dependencies
    pause
    exit /b 1
)
echo ✓ Backend dependencies installed

echo.
echo [3/6] Installing frontend dependencies...
cd ..\frontend
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install frontend dependencies
    pause
    exit /b 1
)
echo ✓ Frontend dependencies installed

echo.
echo [4/6] Setting up database...
cd ..\backend
call npm run seed
if %errorlevel% neq 0 (
    echo WARNING: Database seeding failed. You may need to start MongoDB first.
)
echo ✓ Database setup attempted

echo.
echo [5/6] Creating admin user...
call npm run create-admin
if %errorlevel% neq 0 (
    echo WARNING: Admin user creation failed. You may need to start MongoDB first.
)
echo ✓ Admin user setup attempted

echo.
echo [6/6] Updating product images...
call npm run update-images
if %errorlevel% neq 0 (
    echo WARNING: Image update failed. You may need to start MongoDB first.
)
echo ✓ Product images updated

echo.
echo ========================================
echo           SETUP COMPLETE!
echo ========================================
echo.
echo To run the application:
echo 1. Start MongoDB (if not already running)
echo 2. Open two terminals:
echo    Terminal 1: cd backend && npm start
echo    Terminal 2: cd frontend && npm start
echo.
echo 3. Open http://localhost:3000 in your browser
echo.
echo Admin Login:
echo Email: <EMAIL>
echo Password: admin123
echo.
echo ========================================
pause
