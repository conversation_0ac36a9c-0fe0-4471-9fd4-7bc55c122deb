{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useSearchParams, Link, useNavigate } from 'react-router-dom';\nimport { FiGrid, FiList, FiFilter, FiStar, FiShoppingCart } from 'react-icons/fi';\nimport { getProducts, setFilters } from '../store/slices/productSlice';\nimport { addToCart } from '../store/slices/cartSlice';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  var _categories$find;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showFilters, setShowFilters] = useState(false);\n  const dispatch = useDispatch();\n  const {\n    products,\n    isLoading,\n    totalPages,\n    currentPage,\n    filters\n  } = useSelector(state => state.products);\n  const {\n    categories\n  } = useSelector(state => state.categories);\n  useEffect(() => {\n    const params = {\n      page: searchParams.get('page') || 1,\n      category: searchParams.get('category') || '',\n      search: searchParams.get('search') || '',\n      minPrice: searchParams.get('minPrice') || '',\n      maxPrice: searchParams.get('maxPrice') || '',\n      rating: searchParams.get('rating') || '',\n      sortBy: searchParams.get('sortBy') || 'createdAt',\n      sortOrder: searchParams.get('sortOrder') || 'desc'\n    };\n    dispatch(setFilters(params));\n    dispatch(getProducts(params));\n  }, [searchParams, dispatch]);\n  const handleFilterChange = (key, value) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (value) {\n      newParams.set(key, value);\n    } else {\n      newParams.delete(key);\n    }\n    newParams.set('page', '1'); // Reset to first page\n    setSearchParams(newParams);\n  };\n  const handlePageChange = page => {\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', page);\n    setSearchParams(newParams);\n  };\n  const clearFilters = () => {\n    setSearchParams({});\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [searchParams.get('search') && `Search results for \"${searchParams.get('search')}\"`, searchParams.get('category') && `Category: ${(_categories$find = categories.find(c => c._id === searchParams.get('category'))) === null || _categories$find === void 0 ? void 0 : _categories$find.name}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex bg-white rounded-lg border\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('grid'),\n              className: `p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-l-lg`,\n              children: /*#__PURE__*/_jsxDEV(FiGrid, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('list'),\n              className: `p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-r-lg`,\n              children: /*#__PURE__*/_jsxDEV(FiList, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowFilters(!showFilters),\n            className: \"flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: `${filters.sortBy}-${filters.sortOrder}`,\n            onChange: e => {\n              const [sortBy, sortOrder] = e.target.value.split('-');\n              handleFilterChange('sortBy', sortBy);\n              handleFilterChange('sortOrder', sortOrder);\n            },\n            className: \"bg-white border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"createdAt-desc\",\n              children: \"Newest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 105,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"createdAt-asc\",\n              children: \"Oldest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"price-asc\",\n              children: \"Price: Low to High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"price-desc\",\n              children: \"Price: High to Low\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name-asc\",\n              children: \"Name: A to Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name-desc\",\n              children: \"Name: Z to A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rating.average-desc\",\n              children: \"Highest Rated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-64 bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"text-sm text-green-600 hover:text-green-700\",\n              children: \"Clear All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"category\",\n                  value: category._id,\n                  checked: filters.category === category._id,\n                  onChange: e => handleFilterChange('category', e.target.value),\n                  className: \"text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 23\n                }, this)]\n              }, category._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Price Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"Min Price\",\n                value: filters.minPrice,\n                onChange: e => handleFilterChange('minPrice', e.target.value),\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"Max Price\",\n                value: filters.maxPrice,\n                onChange: e => handleFilterChange('maxPrice', e.target.value),\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [4, 3, 2, 1].map(rating => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"rating\",\n                  value: rating,\n                  checked: filters.rating === rating.toString(),\n                  onChange: e => handleFilterChange('rating', e.target.value),\n                  className: \"text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-2 flex items-center\",\n                  children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FiStar, {\n                    className: `w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 187,\n                    columnNumber: 27\n                  }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-sm\",\n                    children: \"& up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 194,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this)]\n              }, rating, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center py-12\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 text-lg\",\n              children: \"No products found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"mt-4 text-green-600 hover:text-green-700\",\n              children: \"Clear filters to see all products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'}`,\n              children: products.map(product => {\n                var _product$images, _product$images$, _product$rating2;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow ${viewMode === 'list' ? 'flex' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/products/${product._id}`,\n                    className: viewMode === 'list' ? 'flex w-full' : '',\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`,\n                      children: /*#__PURE__*/_jsxDEV(\"img\", {\n                        src: ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : (_product$images$ = _product$images[0]) === null || _product$images$ === void 0 ? void 0 : _product$images$.url) || '/api/placeholder/300/300',\n                        alt: product.name,\n                        className: `w-full object-cover ${viewMode === 'list' ? 'h-32 rounded-l-lg' : 'h-48 rounded-t-lg'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 235,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 234,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4 flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2 hover:text-green-600 transition-colors\",\n                        children: product.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 244,\n                        columnNumber: 27\n                      }, this), viewMode === 'list' && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm mb-2 line-clamp-2\",\n                        children: product.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 248,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [...Array(5)].map((_, i) => {\n                            var _product$rating;\n                            return /*#__PURE__*/_jsxDEV(FiStar, {\n                              className: `w-4 h-4 ${i < Math.floor(((_product$rating = product.rating) === null || _product$rating === void 0 ? void 0 : _product$rating.average) || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                            }, i, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 255,\n                              columnNumber: 33\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 253,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm text-gray-500 ml-2\",\n                          children: [\"(\", ((_product$rating2 = product.rating) === null || _product$rating2 === void 0 ? void 0 : _product$rating2.count) || 0, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 265,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 252,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-lg font-bold text-green-600\",\n                            children: [\"$\", product.discountedPrice || product.price]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 271,\n                            columnNumber: 31\n                          }, this), product.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-sm text-gray-500 line-through\",\n                            children: [\"$\", product.price]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 275,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 270,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          className: \"bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors\",\n                          children: /*#__PURE__*/_jsxDEV(FiShoppingCart, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 281,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 280,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 233,\n                    columnNumber: 23\n                  }, this)\n                }, product._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [...Array(totalPages)].map((_, i) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(i + 1),\n                  className: `px-3 py-2 rounded-lg ${currentPage === i + 1 ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`,\n                  children: i + 1\n                }, i + 1, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"vQ0ZgL8N7eDqNTa+EtzA/BsGQO8=\", false, function () {\n  return [useSearchParams, useDispatch, useSelector, useSelector];\n});\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useSearchParams", "Link", "useNavigate", "<PERSON><PERSON><PERSON>", "FiList", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiShoppingCart", "getProducts", "setFilters", "addToCart", "LoadingSpinner", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Products", "_s", "_categories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "showFilters", "setShowFilters", "dispatch", "products", "isLoading", "totalPages", "currentPage", "filters", "state", "categories", "params", "page", "get", "category", "search", "minPrice", "maxPrice", "rating", "sortBy", "sortOrder", "handleFilterChange", "key", "value", "newParams", "URLSearchParams", "set", "delete", "handlePageChange", "clearFilters", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "find", "c", "_id", "name", "onClick", "onChange", "e", "target", "split", "map", "type", "checked", "placeholder", "toString", "Array", "_", "i", "size", "length", "product", "_product$images", "_product$images$", "_product$rating2", "to", "src", "images", "url", "alt", "description", "_product$rating", "Math", "floor", "average", "count", "discountedPrice", "price", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/Products.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useSearchParams, Link, useNavigate } from 'react-router-dom';\nimport { FiGrid, FiList, FiFilter, FiStar, FiShoppingCart } from 'react-icons/fi';\nimport { getProducts, setFilters } from '../store/slices/productSlice';\nimport { addToCart } from '../store/slices/cartSlice';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\n\nconst Products = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showFilters, setShowFilters] = useState(false);\n  \n  const dispatch = useDispatch();\n  const { products, isLoading, totalPages, currentPage, filters } = useSelector((state) => state.products);\n  const { categories } = useSelector((state) => state.categories);\n\n  useEffect(() => {\n    const params = {\n      page: searchParams.get('page') || 1,\n      category: searchParams.get('category') || '',\n      search: searchParams.get('search') || '',\n      minPrice: searchParams.get('minPrice') || '',\n      maxPrice: searchParams.get('maxPrice') || '',\n      rating: searchParams.get('rating') || '',\n      sortBy: searchParams.get('sortBy') || 'createdAt',\n      sortOrder: searchParams.get('sortOrder') || 'desc',\n    };\n\n    dispatch(setFilters(params));\n    dispatch(getProducts(params));\n  }, [searchParams, dispatch]);\n\n  const handleFilterChange = (key, value) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (value) {\n      newParams.set(key, value);\n    } else {\n      newParams.delete(key);\n    }\n    newParams.set('page', '1'); // Reset to first page\n    setSearchParams(newParams);\n  };\n\n  const handlePageChange = (page) => {\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', page);\n    setSearchParams(newParams);\n  };\n\n  const clearFilters = () => {\n    setSearchParams({});\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Products</h1>\n            <p className=\"text-gray-600\">\n              {searchParams.get('search') && `Search results for \"${searchParams.get('search')}\"`}\n              {searchParams.get('category') && `Category: ${categories.find(c => c._id === searchParams.get('category'))?.name}`}\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-white rounded-lg border\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-l-lg`}\n              >\n                <FiGrid className=\"w-5 h-5\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-r-lg`}\n              >\n                <FiList className=\"w-5 h-5\" />\n              </button>\n            </div>\n\n            {/* Filter Toggle */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border hover:bg-gray-50\"\n            >\n              <FiFilter className=\"w-5 h-5\" />\n              <span>Filters</span>\n            </button>\n\n            {/* Sort */}\n            <select\n              value={`${filters.sortBy}-${filters.sortOrder}`}\n              onChange={(e) => {\n                const [sortBy, sortOrder] = e.target.value.split('-');\n                handleFilterChange('sortBy', sortBy);\n                handleFilterChange('sortOrder', sortOrder);\n              }}\n              className=\"bg-white border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              <option value=\"createdAt-desc\">Newest First</option>\n              <option value=\"createdAt-asc\">Oldest First</option>\n              <option value=\"price-asc\">Price: Low to High</option>\n              <option value=\"price-desc\">Price: High to Low</option>\n              <option value=\"name-asc\">Name: A to Z</option>\n              <option value=\"name-desc\">Name: Z to A</option>\n              <option value=\"rating.average-desc\">Highest Rated</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Filters Sidebar */}\n          {showFilters && (\n            <div className=\"lg:w-64 bg-white rounded-lg shadow-md p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-semibold\">Filters</h3>\n                <button\n                  onClick={clearFilters}\n                  className=\"text-sm text-green-600 hover:text-green-700\"\n                >\n                  Clear All\n                </button>\n              </div>\n\n              {/* Categories */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Categories</h4>\n                <div className=\"space-y-2\">\n                  {categories.map((category) => (\n                    <label key={category._id} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"category\"\n                        value={category._id}\n                        checked={filters.category === category._id}\n                        onChange={(e) => handleFilterChange('category', e.target.value)}\n                        className=\"text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm\">{category.name}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Price Range */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Price Range</h4>\n                <div className=\"space-y-2\">\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min Price\"\n                    value={filters.minPrice}\n                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  />\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max Price\"\n                    value={filters.maxPrice}\n                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  />\n                </div>\n              </div>\n\n              {/* Rating */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Rating</h4>\n                <div className=\"space-y-2\">\n                  {[4, 3, 2, 1].map((rating) => (\n                    <label key={rating} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"rating\"\n                        value={rating}\n                        checked={filters.rating === rating.toString()}\n                        onChange={(e) => handleFilterChange('rating', e.target.value)}\n                        className=\"text-green-600 focus:ring-green-500\"\n                      />\n                      <div className=\"ml-2 flex items-center\">\n                        {[...Array(5)].map((_, i) => (\n                          <FiStar\n                            key={i}\n                            className={`w-4 h-4 ${\n                              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                            }`}\n                          />\n                        ))}\n                        <span className=\"ml-1 text-sm\">& up</span>\n                      </div>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Products Grid/List */}\n          <div className=\"flex-1\">\n            {isLoading ? (\n              <div className=\"flex justify-center py-12\">\n                <LoadingSpinner size=\"lg\" />\n              </div>\n            ) : products.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <p className=\"text-gray-500 text-lg\">No products found</p>\n                <button\n                  onClick={clearFilters}\n                  className=\"mt-4 text-green-600 hover:text-green-700\"\n                >\n                  Clear filters to see all products\n                </button>\n              </div>\n            ) : (\n              <>\n                <div className={`grid gap-6 ${\n                  viewMode === 'grid' \n                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' \n                    : 'grid-cols-1'\n                }`}>\n                  {products.map((product) => (\n                    <div\n                      key={product._id}\n                      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow ${\n                        viewMode === 'list' ? 'flex' : ''\n                      }`}\n                    >\n                      <Link to={`/products/${product._id}`} className={viewMode === 'list' ? 'flex w-full' : ''}>\n                        <div className={`${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n                          <img\n                            src={product.images?.[0]?.url || '/api/placeholder/300/300'}\n                            alt={product.name}\n                            className={`w-full object-cover ${\n                              viewMode === 'list' ? 'h-32 rounded-l-lg' : 'h-48 rounded-t-lg'\n                            }`}\n                          />\n                        </div>\n                        <div className=\"p-4 flex-1\">\n                          <h3 className=\"text-lg font-semibold text-gray-900 mb-2 hover:text-green-600 transition-colors\">\n                            {product.name}\n                          </h3>\n                          {viewMode === 'list' && (\n                            <p className=\"text-gray-600 text-sm mb-2 line-clamp-2\">\n                              {product.description}\n                            </p>\n                          )}\n                          <div className=\"flex items-center mb-2\">\n                            <div className=\"flex items-center\">\n                              {[...Array(5)].map((_, i) => (\n                                <FiStar\n                                  key={i}\n                                  className={`w-4 h-4 ${\n                                    i < Math.floor(product.rating?.average || 0)\n                                      ? 'text-yellow-400 fill-current'\n                                      : 'text-gray-300'\n                                  }`}\n                                />\n                              ))}\n                            </div>\n                            <span className=\"text-sm text-gray-500 ml-2\">\n                              ({product.rating?.count || 0})\n                            </span>\n                          </div>\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex items-center space-x-2\">\n                              <span className=\"text-lg font-bold text-green-600\">\n                                ${product.discountedPrice || product.price}\n                              </span>\n                              {product.discountedPrice && (\n                                <span className=\"text-sm text-gray-500 line-through\">\n                                  ${product.price}\n                                </span>\n                              )}\n                            </div>\n                            <button className=\"bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors\">\n                              <FiShoppingCart className=\"w-4 h-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </Link>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <div className=\"flex justify-center mt-8\">\n                    <div className=\"flex space-x-2\">\n                      {[...Array(totalPages)].map((_, i) => (\n                        <button\n                          key={i + 1}\n                          onClick={() => handlePageChange(i + 1)}\n                          className={`px-3 py-2 rounded-lg ${\n                            currentPage === i + 1\n                              ? 'bg-green-600 text-white'\n                              : 'bg-white text-gray-700 hover:bg-gray-100'\n                          }`}\n                        >\n                          {i + 1}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACrE,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,QAAQ,gBAAgB;AACjF,SAASC,WAAW,EAAEC,UAAU,QAAQ,8BAA8B;AACtE,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,eAAe,CAAC,CAAC;EACzD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM6B,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,QAAQ;IAAEC,SAAS;IAAEC,UAAU;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGhC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACL,QAAQ,CAAC;EACxG,MAAM;IAAEM;EAAW,CAAC,GAAGlC,WAAW,CAAEiC,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAE/DrC,SAAS,CAAC,MAAM;IACd,MAAMsC,MAAM,GAAG;MACbC,IAAI,EAAEf,YAAY,CAACgB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;MACnCC,QAAQ,EAAEjB,YAAY,CAACgB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CE,MAAM,EAAElB,YAAY,CAACgB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxCG,QAAQ,EAAEnB,YAAY,CAACgB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CI,QAAQ,EAAEpB,YAAY,CAACgB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CK,MAAM,EAAErB,YAAY,CAACgB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxCM,MAAM,EAAEtB,YAAY,CAACgB,GAAG,CAAC,QAAQ,CAAC,IAAI,WAAW;MACjDO,SAAS,EAAEvB,YAAY,CAACgB,GAAG,CAAC,WAAW,CAAC,IAAI;IAC9C,CAAC;IAEDV,QAAQ,CAACjB,UAAU,CAACyB,MAAM,CAAC,CAAC;IAC5BR,QAAQ,CAAClB,WAAW,CAAC0B,MAAM,CAAC,CAAC;EAC/B,CAAC,EAAE,CAACd,YAAY,EAAEM,QAAQ,CAAC,CAAC;EAE5B,MAAMkB,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzC,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAAC5B,YAAY,CAAC;IACnD,IAAI0B,KAAK,EAAE;MACTC,SAAS,CAACE,GAAG,CAACJ,GAAG,EAAEC,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLC,SAAS,CAACG,MAAM,CAACL,GAAG,CAAC;IACvB;IACAE,SAAS,CAACE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5B5B,eAAe,CAAC0B,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMI,gBAAgB,GAAIhB,IAAI,IAAK;IACjC,MAAMY,SAAS,GAAG,IAAIC,eAAe,CAAC5B,YAAY,CAAC;IACnD2B,SAAS,CAACE,GAAG,CAAC,MAAM,EAAEd,IAAI,CAAC;IAC3Bd,eAAe,CAAC0B,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB/B,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,oBACEP,OAAA;IAAKuC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCxC,OAAA;MAAKuC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DxC,OAAA;QAAKuC,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACzFxC,OAAA;UAAAwC,QAAA,gBACExC,OAAA;YAAIuC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE5C,OAAA;YAAGuC,SAAS,EAAC,eAAe;YAAAC,QAAA,GACzBlC,YAAY,CAACgB,GAAG,CAAC,QAAQ,CAAC,IAAI,uBAAuBhB,YAAY,CAACgB,GAAG,CAAC,QAAQ,CAAC,GAAG,EAClFhB,YAAY,CAACgB,GAAG,CAAC,UAAU,CAAC,IAAI,cAAAjB,gBAAA,GAAac,UAAU,CAAC0B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKzC,YAAY,CAACgB,GAAG,CAAC,UAAU,CAAC,CAAC,cAAAjB,gBAAA,uBAA5DA,gBAAA,CAA8D2C,IAAI,EAAE;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN5C,OAAA;UAAKuC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAEvDxC,OAAA;YAAKuC,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CxC,OAAA;cACEiD,OAAO,EAAEA,CAAA,KAAMxC,WAAW,CAAC,MAAM,CAAE;cACnC8B,SAAS,EAAE,OAAO/B,QAAQ,KAAK,MAAM,GAAG,yBAAyB,GAAG,eAAe,eAAgB;cAAAgC,QAAA,eAEnGxC,OAAA,CAACX,MAAM;gBAACkD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACT5C,OAAA;cACEiD,OAAO,EAAEA,CAAA,KAAMxC,WAAW,CAAC,MAAM,CAAE;cACnC8B,SAAS,EAAE,OAAO/B,QAAQ,KAAK,MAAM,GAAG,yBAAyB,GAAG,eAAe,eAAgB;cAAAgC,QAAA,eAEnGxC,OAAA,CAACV,MAAM;gBAACiD,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5C,OAAA;YACEiD,OAAO,EAAEA,CAAA,KAAMtC,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5C6B,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAE7FxC,OAAA,CAACT,QAAQ;cAACgD,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC5C,OAAA;cAAAwC,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAGT5C,OAAA;YACEgC,KAAK,EAAE,GAAGf,OAAO,CAACW,MAAM,IAAIX,OAAO,CAACY,SAAS,EAAG;YAChDqB,QAAQ,EAAGC,CAAC,IAAK;cACf,MAAM,CAACvB,MAAM,EAAEC,SAAS,CAAC,GAAGsB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAACqB,KAAK,CAAC,GAAG,CAAC;cACrDvB,kBAAkB,CAAC,QAAQ,EAAEF,MAAM,CAAC;cACpCE,kBAAkB,CAAC,WAAW,EAAED,SAAS,CAAC;YAC5C,CAAE;YACFU,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAErGxC,OAAA;cAAQgC,KAAK,EAAC,gBAAgB;cAAAQ,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpD5C,OAAA;cAAQgC,KAAK,EAAC,eAAe;cAAAQ,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnD5C,OAAA;cAAQgC,KAAK,EAAC,WAAW;cAAAQ,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrD5C,OAAA;cAAQgC,KAAK,EAAC,YAAY;cAAAQ,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtD5C,OAAA;cAAQgC,KAAK,EAAC,UAAU;cAAAQ,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C5C,OAAA;cAAQgC,KAAK,EAAC,WAAW;cAAAQ,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C5C,OAAA;cAAQgC,KAAK,EAAC,qBAAqB;cAAAQ,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN5C,OAAA;QAAKuC,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAE7C9B,WAAW,iBACVV,OAAA;UAAKuC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDxC,OAAA;YAAKuC,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDxC,OAAA;cAAIuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClD5C,OAAA;cACEiD,OAAO,EAAEX,YAAa;cACtBC,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EACxD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN5C,OAAA;YAAKuC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxC,OAAA;cAAIuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBrB,UAAU,CAACmC,GAAG,CAAE/B,QAAQ,iBACvBvB,OAAA;gBAA0BuC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACrDxC,OAAA;kBACEuD,IAAI,EAAC,OAAO;kBACZP,IAAI,EAAC,UAAU;kBACfhB,KAAK,EAAET,QAAQ,CAACwB,GAAI;kBACpBS,OAAO,EAAEvC,OAAO,CAACM,QAAQ,KAAKA,QAAQ,CAACwB,GAAI;kBAC3CG,QAAQ,EAAGC,CAAC,IAAKrB,kBAAkB,CAAC,UAAU,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;kBAChEO,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACF5C,OAAA;kBAAMuC,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEjB,QAAQ,CAACyB;gBAAI;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAT3CrB,QAAQ,CAACwB,GAAG;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUjB,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5C,OAAA;YAAKuC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxC,OAAA;cAAIuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBxC,OAAA;gBACEuD,IAAI,EAAC,QAAQ;gBACbE,WAAW,EAAC,WAAW;gBACvBzB,KAAK,EAAEf,OAAO,CAACQ,QAAS;gBACxByB,QAAQ,EAAGC,CAAC,IAAKrB,kBAAkB,CAAC,UAAU,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;gBAChEO,SAAS,EAAC;cAAyF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACF5C,OAAA;gBACEuD,IAAI,EAAC,QAAQ;gBACbE,WAAW,EAAC,WAAW;gBACvBzB,KAAK,EAAEf,OAAO,CAACS,QAAS;gBACxBwB,QAAQ,EAAGC,CAAC,IAAKrB,kBAAkB,CAAC,UAAU,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;gBAChEO,SAAS,EAAC;cAAyF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5C,OAAA;YAAKuC,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBxC,OAAA;cAAIuC,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C5C,OAAA;cAAKuC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACc,GAAG,CAAE3B,MAAM,iBACvB3B,OAAA;gBAAoBuC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/CxC,OAAA;kBACEuD,IAAI,EAAC,OAAO;kBACZP,IAAI,EAAC,QAAQ;kBACbhB,KAAK,EAAEL,MAAO;kBACd6B,OAAO,EAAEvC,OAAO,CAACU,MAAM,KAAKA,MAAM,CAAC+B,QAAQ,CAAC,CAAE;kBAC9CR,QAAQ,EAAGC,CAAC,IAAKrB,kBAAkB,CAAC,QAAQ,EAAEqB,CAAC,CAACC,MAAM,CAACpB,KAAK,CAAE;kBAC9DO,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACF5C,OAAA;kBAAKuC,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,GACpC,CAAC,GAAGmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,kBACtB7D,OAAA,CAACR,MAAM;oBAEL+C,SAAS,EAAE,WACTsB,CAAC,GAAGlC,MAAM,GAAG,8BAA8B,GAAG,eAAe;kBAC5D,GAHEkC,CAAC;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIP,CACF,CAAC,eACF5C,OAAA;oBAAMuC,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA,GAnBIjB,MAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD5C,OAAA;UAAKuC,SAAS,EAAC,QAAQ;UAAAC,QAAA,EACpB1B,SAAS,gBACRd,OAAA;YAAKuC,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCxC,OAAA,CAACH,cAAc;cAACiE,IAAI,EAAC;YAAI;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,GACJ/B,QAAQ,CAACkD,MAAM,KAAK,CAAC,gBACvB/D,OAAA;YAAKuC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCxC,OAAA;cAAGuC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D5C,OAAA;cACEiD,OAAO,EAAEX,YAAa;cACtBC,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN5C,OAAA,CAAAE,SAAA;YAAAsC,QAAA,gBACExC,OAAA;cAAKuC,SAAS,EAAE,cACd/B,QAAQ,KAAK,MAAM,GACf,0DAA0D,GAC1D,aAAa,EAChB;cAAAgC,QAAA,EACA3B,QAAQ,CAACyC,GAAG,CAAEU,OAAO;gBAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;gBAAA,oBACpBnE,OAAA;kBAEEuC,SAAS,EAAE,mEACT/B,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,EAChC;kBAAAgC,QAAA,eAEHxC,OAAA,CAACb,IAAI;oBAACiF,EAAE,EAAE,aAAaJ,OAAO,CAACjB,GAAG,EAAG;oBAACR,SAAS,EAAE/B,QAAQ,KAAK,MAAM,GAAG,aAAa,GAAG,EAAG;oBAAAgC,QAAA,gBACxFxC,OAAA;sBAAKuC,SAAS,EAAE,GAAG/B,QAAQ,KAAK,MAAM,GAAG,oBAAoB,GAAG,EAAE,EAAG;sBAAAgC,QAAA,eACnExC,OAAA;wBACEqE,GAAG,EAAE,EAAAJ,eAAA,GAAAD,OAAO,CAACM,MAAM,cAAAL,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,uBAAnBA,gBAAA,CAAqBK,GAAG,KAAI,0BAA2B;wBAC5DC,GAAG,EAAER,OAAO,CAAChB,IAAK;wBAClBT,SAAS,EAAE,uBACT/B,QAAQ,KAAK,MAAM,GAAG,mBAAmB,GAAG,mBAAmB;sBAC9D;wBAAAiC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACN5C,OAAA;sBAAKuC,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBxC,OAAA;wBAAIuC,SAAS,EAAC,iFAAiF;wBAAAC,QAAA,EAC5FwB,OAAO,CAAChB;sBAAI;wBAAAP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACX,CAAC,EACJpC,QAAQ,KAAK,MAAM,iBAClBR,OAAA;wBAAGuC,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,EACnDwB,OAAO,CAACS;sBAAW;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACJ,eACD5C,OAAA;wBAAKuC,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCxC,OAAA;0BAAKuC,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAC/B,CAAC,GAAGmB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC;4BAAA,IAAAa,eAAA;4BAAA,oBACtB1E,OAAA,CAACR,MAAM;8BAEL+C,SAAS,EAAE,WACTsB,CAAC,GAAGc,IAAI,CAACC,KAAK,CAAC,EAAAF,eAAA,GAAAV,OAAO,CAACrC,MAAM,cAAA+C,eAAA,uBAAdA,eAAA,CAAgBG,OAAO,KAAI,CAAC,CAAC,GACxC,8BAA8B,GAC9B,eAAe;4BAClB,GALEhB,CAAC;8BAAApB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAMP,CAAC;0BAAA,CACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN5C,OAAA;0BAAMuC,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,GAAC,GAC1C,EAAC,EAAA2B,gBAAA,GAAAH,OAAO,CAACrC,MAAM,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBW,KAAK,KAAI,CAAC,EAAC,GAC/B;wBAAA;0BAAArC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN5C,OAAA;wBAAKuC,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDxC,OAAA;0BAAKuC,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1CxC,OAAA;4BAAMuC,SAAS,EAAC,kCAAkC;4BAAAC,QAAA,GAAC,GAChD,EAACwB,OAAO,CAACe,eAAe,IAAIf,OAAO,CAACgB,KAAK;0BAAA;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC,EACNoB,OAAO,CAACe,eAAe,iBACtB/E,OAAA;4BAAMuC,SAAS,EAAC,oCAAoC;4BAAAC,QAAA,GAAC,GAClD,EAACwB,OAAO,CAACgB,KAAK;0BAAA;4BAAAvC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACX,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACN5C,OAAA;0BAAQuC,SAAS,EAAC,6EAA6E;0BAAAC,QAAA,eAC7FxC,OAAA,CAACP,cAAc;4BAAC8C,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAzDFoB,OAAO,CAACjB,GAAG;kBAAAN,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Db,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGL7B,UAAU,GAAG,CAAC,iBACbf,OAAA;cAAKuC,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCxC,OAAA;gBAAKuC,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B,CAAC,GAAGmB,KAAK,CAAC5C,UAAU,CAAC,CAAC,CAACuC,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,kBAC/B7D,OAAA;kBAEEiD,OAAO,EAAEA,CAAA,KAAMZ,gBAAgB,CAACwB,CAAC,GAAG,CAAC,CAAE;kBACvCtB,SAAS,EAAE,wBACTvB,WAAW,KAAK6C,CAAC,GAAG,CAAC,GACjB,yBAAyB,GACzB,0CAA0C,EAC7C;kBAAArB,QAAA,EAEFqB,CAAC,GAAG;gBAAC,GARDA,CAAC,GAAG,CAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAnTID,QAAQ;EAAA,QAC4BjB,eAAe,EAItCF,WAAW,EACsCC,WAAW,EACtDA,WAAW;AAAA;AAAAgG,EAAA,GAP9B9E,QAAQ;AAqTd,eAAeA,QAAQ;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}