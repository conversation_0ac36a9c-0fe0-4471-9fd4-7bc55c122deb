const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

// Import routes
const authRoutes = require('./routes/auth');
const productRoutes = require('./routes/products');
const cartRoutes = require('./routes/cart');
const orderRoutes = require('./routes/orders');
const categoryRoutes = require('./routes/categories');
const adminRoutes = require('./routes/admin');
const paymentRoutes = require('./routes/payment');

// Import middleware
const { errorHandler } = require('./middleware/errorHandler');

const app = express();

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Static files
app.use('/uploads', express.static('uploads'));

// Placeholder image route
app.get('/api/placeholder/:width/:height', (req, res) => {
  const { width, height } = req.params;
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="16" fill="#9ca3af" text-anchor="middle" dy=".3em">
        ${width}x${height}
      </text>
    </svg>
  `;
  res.setHeader('Content-Type', 'image/svg+xml');
  res.send(svg);
});

// Handle missing product images with attractive placeholders
app.get('/images/products/:filename', (req, res) => {
  const filename = req.params.filename;
  const productName = filename.replace('.jpg', '').replace('.png', '').replace(/-/g, ' ');

  // Color schemes for different product categories
  const colorSchemes = {
    // Fruits
    'apple': { bg: '#ff6b6b', accent: '#ff5252', icon: '🍎' },
    'banana': { bg: '#ffd93d', accent: '#ffcd02', icon: '🍌' },
    'orange': { bg: '#ff9500', accent: '#ff8c00', icon: '🍊' },
    'grape': { bg: '#9c27b0', accent: '#8e24aa', icon: '🍇' },
    'strawberry': { bg: '#e91e63', accent: '#d81b60', icon: '🍓' },
    'blueberry': { bg: '#3f51b5', accent: '#3949ab', icon: '🫐' },
    'mango': { bg: '#ff9800', accent: '#f57c00', icon: '🥭' },
    'pineapple': { bg: '#ffc107', accent: '#ffb300', icon: '🍍' },
    'watermelon': { bg: '#4caf50', accent: '#43a047', icon: '🍉' },
    'peach': { bg: '#ffab91', accent: '#ff8a65', icon: '🍑' },

    // Vegetables
    'tomato': { bg: '#f44336', accent: '#e53935', icon: '🍅' },
    'carrot': { bg: '#ff9800', accent: '#f57c00', icon: '🥕' },
    'broccoli': { bg: '#4caf50', accent: '#43a047', icon: '🥦' },
    'potato': { bg: '#8d6e63', accent: '#795548', icon: '🥔' },
    'onion': { bg: '#ffc107', accent: '#ffb300', icon: '🧅' },
    'garlic': { bg: '#f5f5f5', accent: '#e0e0e0', icon: '🧄' },
    'pepper': { bg: '#ff5722', accent: '#f4511e', icon: '🌶️' },
    'cucumber': { bg: '#4caf50', accent: '#43a047', icon: '🥒' },
    'lettuce': { bg: '#8bc34a', accent: '#7cb342', icon: '🥬' },

    // Dairy
    'milk': { bg: '#ffffff', accent: '#f5f5f5', icon: '🥛' },
    'cheese': { bg: '#ffc107', accent: '#ffb300', icon: '🧀' },
    'yogurt': { bg: '#e1f5fe', accent: '#b3e5fc', icon: '🥛' },
    'butter': { bg: '#fff3e0', accent: '#ffe0b2', icon: '🧈' },

    // Default
    'default': { bg: '#e3f2fd', accent: '#bbdefb', icon: '🛒' }
  };

  // Find matching color scheme
  let scheme = colorSchemes.default;
  for (const [key, value] of Object.entries(colorSchemes)) {
    if (productName.toLowerCase().includes(key)) {
      scheme = value;
      break;
    }
  }

  const svg = `
    <svg width="300" height="300" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${scheme.bg};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${scheme.accent};stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
      <circle cx="150" cy="120" r="40" fill="rgba(255,255,255,0.2)"/>
      <text x="50%" y="35%" font-family="Arial, sans-serif" font-size="48" text-anchor="middle" dy=".3em">
        ${scheme.icon}
      </text>
      <text x="50%" y="65%" font-family="Arial, sans-serif" font-size="16" fill="rgba(0,0,0,0.8)" text-anchor="middle" dy=".3em" font-weight="bold">
        ${productName.charAt(0).toUpperCase() + productName.slice(1)}
      </text>
      <text x="50%" y="75%" font-family="Arial, sans-serif" font-size="12" fill="rgba(0,0,0,0.6)" text-anchor="middle" dy=".3em">
        Fresh & Quality
      </text>
    </svg>
  `;
  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
  res.send(svg);
});

// Database connection
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grocery-store', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
})
.then(() => console.log('✅ MongoDB connected successfully'))
.catch(err => console.error('❌ MongoDB connection error:', err));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/payment', paymentRoutes);

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Error handling middleware
app.use(errorHandler);

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
});

module.exports = app;
