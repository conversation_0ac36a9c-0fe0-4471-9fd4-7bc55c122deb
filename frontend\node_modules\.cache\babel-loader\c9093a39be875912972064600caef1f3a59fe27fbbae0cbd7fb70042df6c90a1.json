{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const location = useLocation();\n  if (!user || !token) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"fjjMcPcT7iY1Lq+xahhWpv5Jkdc=\", false, function () {\n  return [useSelector, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useSelector", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "token", "state", "auth", "location", "to", "from", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\n\nconst ProtectedRoute = ({ children }) => {\n  const { user, token } = useSelector((state) => state.auth);\n  const location = useLocation();\n\n  if (!user || !token) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGP,WAAW,CAAEQ,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC1D,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,IAAI,CAACO,IAAI,IAAI,CAACC,KAAK,EAAE;IACnB,oBAAOL,OAAA,CAACJ,QAAQ;MAACa,EAAE,EAAC,QAAQ;MAACH,KAAK,EAAE;QAAEI,IAAI,EAAEF;MAAS,CAAE;MAACG,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,OAAOb,QAAQ;AACjB,CAAC;AAACC,EAAA,CATIF,cAAc;EAAA,QACMH,WAAW,EAClBD,WAAW;AAAA;AAAAmB,EAAA,GAFxBf,cAAc;AAWpB,eAAeA,cAAc;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}