// Simple test to check if API is working
const axios = require('axios');

const testAPI = async () => {
  try {
    console.log('Testing API connection...');
    
    // Test featured products
    const response = await axios.get('http://localhost:5000/api/products/featured');
    console.log('API Response Status:', response.status);
    console.log('API Response Data:', {
      success: response.data.success,
      count: response.data.count,
      productsLength: response.data.products?.length,
      firstProduct: response.data.products?.[0]?.name
    });
    
    if (response.data.products && response.data.products.length > 0) {
      console.log('✅ API is working correctly');
      console.log('Sample products:');
      response.data.products.slice(0, 3).forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} - ₹${product.price}`);
      });
    } else {
      console.log('❌ API returned no products');
    }
    
  } catch (error) {
    console.error('❌ API Error:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
};

testAPI();
