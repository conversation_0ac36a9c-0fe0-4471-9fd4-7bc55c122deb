const mongoose = require('mongoose');
const Product = require('../models/Product');
const productImages = require('../data/productImages');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grocery-store')
  .then(() => console.log('✅ MongoDB connected for image update'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// Function to get image URL for a product
function getImageForProduct(productName) {
  const slug = productName.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();

  // Direct match
  if (productImages[slug]) {
    return productImages[slug];
  }

  // Partial matches for common products
  const partialMatches = {
    'apple': productImages['red-apples'],
    'banana': productImages['bananas'],
    'orange': productImages['oranges'],
    'grape': productImages['grapes'],
    'strawberry': productImages['strawberries'],
    'blueberry': productImages['blueberries'],
    'mango': productImages['mangoes'],
    'tomato': productImages['tomatoes'],
    'potato': productImages['potatoes'],
    'onion': productImages['onions'],
    'carrot': productImages['carrots'],
    'pepper': productImages['bell-peppers'],
    'milk': productImages['milk'],
    'cheese': productImages['cheese'],
    'bread': productImages['bread'],
    'chicken': productImages['chicken-breast'],
    'beef': productImages['ground-beef'],
    'fish': productImages['salmon'],
    'rice': productImages['rice'],
    'pasta': productImages['pasta'],
    'oil': productImages['olive-oil'],
    'juice': productImages['orange-juice'],
    'coffee': productImages['coffee'],
    'tea': productImages['tea'],
    'water': productImages['water'],
    'ice': productImages['ice-cream'],
    'toilet': productImages['toilet-paper'],
    'paper': productImages['paper-towels'],
    'soap': productImages['soap'],
    'shampoo': productImages['shampoo'],
    'vitamin': productImages['vitamins']
  };

  // Check for partial matches
  for (const [key, imageUrl] of Object.entries(partialMatches)) {
    if (slug.includes(key)) {
      return imageUrl;
    }
  }

  // Default fallback - use a generic grocery image
  return 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=400&h=400&fit=crop&crop=center';
}

async function updateProductImages() {
  try {
    console.log('🖼️ Starting product image update...');
    
    // Get all products
    const products = await Product.find({});
    console.log(`📦 Found ${products.length} products to update`);

    let updatedCount = 0;
    
    for (const product of products) {
      const imageUrl = getImageForProduct(product.name);
      
      // Update the product's image
      product.images = [{
        url: imageUrl,
        alt: product.name,
        isPrimary: true
      }];
      
      await product.save();
      updatedCount++;
      
      if (updatedCount % 50 === 0) {
        console.log(`📸 Updated ${updatedCount}/${products.length} products...`);
      }
    }
    
    console.log(`✅ Successfully updated ${updatedCount} products with real images!`);
    console.log('🎨 All products now have high-quality images from Unsplash');
    
  } catch (error) {
    console.error('❌ Error updating product images:', error);
  } finally {
    mongoose.connection.close();
  }
}

updateProductImages();
