const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Cart = require('../models/Cart');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Create payment intent
// @route   POST /api/payment/create-intent
// @access  Private
const createPaymentIntent = asyncHandler(async (req, res, next) => {
  const { shippingAddress, billingAddress } = req.body;

  // Get user cart
  const cart = await Cart.getCartWithProducts(req.user._id);
  if (!cart || cart.items.length === 0) {
    return next(new ErrorResponse('Cart is empty', 400));
  }

  // Validate cart items
  const { validItems, invalidItems } = await cart.validateItems();
  if (invalidItems.length > 0) {
    return next(new ErrorResponse('Some items in your cart are no longer available', 400));
  }

  // Calculate total amount (in cents for Stripe)
  const amount = Math.round(cart.finalPrice * 100);

  try {
    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      metadata: {
        userId: req.user._id.toString(),
        cartId: cart._id.toString(),
        customerEmail: req.user.email
      },
      shipping: {
        name: `${shippingAddress.firstName} ${shippingAddress.lastName}`,
        address: {
          line1: shippingAddress.street,
          city: shippingAddress.city,
          state: shippingAddress.state,
          postal_code: shippingAddress.zipCode,
          country: shippingAddress.country || 'US'
        }
      }
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id,
      amount: cart.finalPrice
    });
  } catch (error) {
    console.error('Stripe payment intent creation failed:', error);
    return next(new ErrorResponse('Payment processing failed', 500));
  }
});

// @desc    Confirm payment and create order
// @route   POST /api/payment/confirm
// @access  Private
const confirmPayment = asyncHandler(async (req, res, next) => {
  const { paymentIntentId, shippingAddress, billingAddress } = req.body;

  try {
    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return next(new ErrorResponse('Payment not completed', 400));
    }

    // Get user cart
    const cart = await Cart.getCartWithProducts(req.user._id);
    if (!cart || cart.items.length === 0) {
      return next(new ErrorResponse('Cart is empty', 400));
    }

    // Validate cart items one more time
    const { validItems, invalidItems } = await cart.validateItems();
    if (invalidItems.length > 0) {
      return next(new ErrorResponse('Some items in your cart are no longer available', 400));
    }

    // Calculate order summary
    const subtotal = cart.totalPrice;
    const tax = subtotal * 0.08; // 8% tax rate
    const shipping = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
    const discount = cart.discountAmount;
    const total = subtotal + tax + shipping - discount;

    // Create order
    const order = new Order({
      user: req.user._id,
      items: cart.items.map(item => ({
        product: item.product._id,
        name: item.product.name,
        price: item.price,
        quantity: item.quantity,
        image: item.product.images[0]?.url || '',
        sku: item.product.sku
      })),
      orderSummary: {
        subtotal,
        tax,
        shipping,
        discount,
        total
      },
      appliedCoupons: cart.appliedCoupons,
      shippingAddress,
      billingAddress: billingAddress || { ...shippingAddress, sameAsShipping: true },
      paymentInfo: {
        method: 'stripe',
        status: 'completed',
        transactionId: paymentIntent.id,
        paymentIntentId: paymentIntent.id,
        paidAt: new Date()
      },
      orderStatus: 'confirmed'
    });

    await order.save();

    // Update product stock
    for (const item of cart.items) {
      const product = await Product.findById(item.product._id);
      if (product && product.stock.trackInventory) {
        product.stock.quantity -= item.quantity;
        await product.save();
      }
    }

    // Mark applied coupons as used
    for (const appliedCoupon of cart.appliedCoupons) {
      const Coupon = require('../models/Coupon');
      const coupon = await Coupon.findByCode(appliedCoupon.code);
      if (coupon) {
        await coupon.markAsUsed(req.user._id, order.orderNumber);
      }
    }

    // Clear cart
    await cart.clearCart();

    // Send order confirmation email
    try {
      const nodemailer = require('nodemailer');
      const transporter = nodemailer.createTransporter({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });

      await transporter.sendMail({
        from: process.env.EMAIL_USER,
        to: req.user.email,
        subject: `Order Confirmation - ${order.orderNumber}`,
        html: `
          <h1>Order Confirmation</h1>
          <p>Thank you for your order!</p>
          <p><strong>Order Number:</strong> ${order.orderNumber}</p>
          <p><strong>Total:</strong> $${total.toFixed(2)}</p>
          <p>We'll send you another email when your order ships.</p>
        `
      });
    } catch (emailError) {
      console.error('Order confirmation email failed:', emailError);
      // Don't fail the order if email fails
    }

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      order: {
        orderNumber: order.orderNumber,
        total: order.orderSummary.total,
        status: order.orderStatus,
        estimatedDelivery: order.estimatedDeliveryDate
      }
    });
  } catch (error) {
    console.error('Payment confirmation failed:', error);
    return next(new ErrorResponse('Payment confirmation failed', 500));
  }
});

// @desc    Handle Stripe webhook
// @route   POST /api/payment/webhook
// @access  Public (Stripe webhook)
const handleWebhook = asyncHandler(async (req, res, next) => {
  const sig = req.headers['stripe-signature'];
  const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  // Handle the event
  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      console.log('Payment succeeded:', paymentIntent.id);
      // Additional processing if needed
      break;

    case 'payment_intent.payment_failed':
      const failedPayment = event.data.object;
      console.log('Payment failed:', failedPayment.id);
      // Handle failed payment
      break;

    default:
      console.log(`Unhandled event type ${event.type}`);
  }

  res.status(200).json({ received: true });
});

// @desc    Get payment methods
// @route   GET /api/payment/methods
// @access  Private
const getPaymentMethods = asyncHandler(async (req, res, next) => {
  // This would typically retrieve saved payment methods for the user
  // For now, we'll return available payment options
  
  res.status(200).json({
    success: true,
    methods: [
      {
        id: 'stripe',
        name: 'Credit/Debit Card',
        type: 'card',
        enabled: true
      },
      {
        id: 'cash_on_delivery',
        name: 'Cash on Delivery',
        type: 'cod',
        enabled: true
      }
    ]
  });
});

// @desc    Refund payment
// @route   POST /api/payment/refund
// @access  Private (Admin)
const refundPayment = asyncHandler(async (req, res, next) => {
  const { orderId, amount, reason } = req.body;

  // Find order
  const order = await Order.findById(orderId);
  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  if (order.paymentInfo.method !== 'stripe') {
    return next(new ErrorResponse('Only Stripe payments can be refunded through this endpoint', 400));
  }

  try {
    // Create refund in Stripe
    const refund = await stripe.refunds.create({
      payment_intent: order.paymentInfo.paymentIntentId,
      amount: amount ? Math.round(amount * 100) : undefined, // Partial or full refund
      reason: 'requested_by_customer'
    });

    // Update order
    await order.processRefund(amount || order.orderSummary.total, reason);

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      refund: {
        id: refund.id,
        amount: refund.amount / 100,
        status: refund.status
      }
    });
  } catch (error) {
    console.error('Refund failed:', error);
    return next(new ErrorResponse('Refund processing failed', 500));
  }
});

module.exports = {
  createPaymentIntent,
  confirmPayment,
  handleWebhook,
  getPaymentMethods,
  refundPayment
};
