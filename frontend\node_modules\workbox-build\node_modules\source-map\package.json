{"name": "source-map", "description": "Generates and consumes source maps", "version": "0.8.0-beta.0", "homepage": "https://github.com/mozilla/source-map", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "usrbincc <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> Smith <<EMAIL>>", "<PERSON> <<EMAIL>>", "azu <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <j<PERSON><PERSON>@walmartlabs.com>", "<PERSON> <jeff<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "djchie <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": {"type": "git", "url": "http://github.com/mozilla/source-map.git"}, "main": "./source-map.js", "types": "./source-map.d.ts", "browser": {"./lib/url.js": "./lib/url-browser.js", "./lib/read-wasm.js": "./lib/read-wasm-browser.js"}, "files": ["source-map.js", "source-map.d.ts", "lib/"], "publishConfig": {"tag": "next"}, "engines": {"node": ">= 8"}, "license": "BSD-3-<PERSON><PERSON>", "scripts": {"lint": "eslint *.js lib/ test/", "prebuild": "npm run lint", "test": "node test/run-tests.js", "coverage": "nyc node test/run-tests.js", "setup": "mkdir -p coverage && cp -n .waiting.html coverage/index.html || true", "dev:live": "live-server --port=4103 --ignorePattern='(js|css|png)$' coverage", "dev:watch": "watch 'npm run coverage' lib/ test/", "predev": "npm run setup", "dev": "npm-run-all -p --silent dev:*", "clean": "rm -rf coverage .nyc_output", "toc": "doctoc --title '## Table of Contents' README.md && doctoc --title '## Table of Contents' CONTRIBUTING.md"}, "devDependencies": {"doctoc": "^1.3.1", "eslint": "^4.19.1", "live-server": "^1.2.0", "npm-run-all": "^4.1.2", "nyc": "^11.7.1", "watch": "^1.0.2"}, "nyc": {"reporter": "html"}, "typings": "source-map", "dependencies": {"whatwg-url": "^7.0.0"}}