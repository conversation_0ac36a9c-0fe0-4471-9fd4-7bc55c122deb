{"name": "global-prefix", "description": "Get the npm global path prefix.", "version": "3.0.0", "homepage": "https://github.com/jonschlinkert/global-prefix", "author": "<PERSON> (https://github.com/jonschlinkert)", "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/rmbaad)", "<PERSON> (https://twitter.com/doowb)", "Charl<PERSON> Reagent (https://i.am.charlike.online)", "<PERSON><PERSON><PERSON> (https://packagist.org/packages/jason-chang)", "<PERSON> (http://twitter.com/jonschlink<PERSON>)", "<PERSON><PERSON><PERSON> (https://www.ncode.nl)", "<PERSON> (chrome://dino)", "<PERSON> (http://rossfenning.co.uk)"], "repository": "jonschlinkert/global-prefix", "bugs": {"url": "https://github.com/jonschlinkert/global-prefix/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=6"}, "scripts": {"test": "mocha"}, "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "devDependencies": {"gulp-format-md": "^2.0.0", "mocha": "^5.2.0"}, "keywords": ["global", "module", "modules", "npm", "path", "prefix", "resolve"]}