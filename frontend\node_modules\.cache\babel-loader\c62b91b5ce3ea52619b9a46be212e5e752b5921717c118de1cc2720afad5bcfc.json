{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\components\\\\common\\\\ErrorBoundary.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console in development\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"mx-auto h-12 w-12 text-red-500\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Oops! Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: \"We're sorry, but something unexpected happened. Please try refreshing the page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors\",\n              children: \"Refresh Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 42,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/',\n              className: \"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\",\n              children: \"Go to Home\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-4 text-left\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"cursor-pointer text-sm text-gray-500\",\n              children: \"Error Details (Development)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n              className: \"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto\",\n              children: [this.state.error.toString(), this.state.errorInfo.componentStack]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "error", "errorInfo", "getDerivedStateFromError", "componentDidCatch", "console", "setState", "render", "className", "children", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "href", "process", "env", "NODE_ENV", "toString", "componentStack"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/components/common/ErrorBoundary.js"], "sourcesContent": ["import React from 'react';\n\nclass ErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = { hasError: false, error: null, errorInfo: null };\n  }\n\n  static getDerivedStateFromError(error) {\n    // Update state so the next render will show the fallback UI\n    return { hasError: true };\n  }\n\n  componentDidCatch(error, errorInfo) {\n    // Log the error to console in development\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    \n    this.setState({\n      error: error,\n      errorInfo: errorInfo\n    });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      // Fallback UI\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"mb-4\">\n              <svg className=\"mx-auto h-12 w-12 text-red-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n              Oops! Something went wrong\n            </h2>\n            <p className=\"text-gray-600 mb-4\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n            <div className=\"space-y-2\">\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors\"\n              >\n                Refresh Page\n              </button>\n              <button\n                onClick={() => window.location.href = '/'}\n                className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors\"\n              >\n                Go to Home\n              </button>\n            </div>\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-4 text-left\">\n                <summary className=\"cursor-pointer text-sm text-gray-500\">\n                  Error Details (Development)\n                </summary>\n                <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto\">\n                  {this.state.error.toString()}\n                  {this.state.errorInfo.componentStack}\n                </pre>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,SAASH,KAAK,CAACI,SAAS,CAAC;EAC1CC,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAE;IAAK,CAAC;EAChE;EAEA,OAAOC,wBAAwBA,CAACF,KAAK,EAAE;IACrC;IACA,OAAO;MAAED,QAAQ,EAAE;IAAK,CAAC;EAC3B;EAEAI,iBAAiBA,CAACH,KAAK,EAAEC,SAAS,EAAE;IAClC;IACAG,OAAO,CAACJ,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEC,SAAS,CAAC;IAEjE,IAAI,CAACI,QAAQ,CAAC;MACZL,KAAK,EAAEA,KAAK;MACZC,SAAS,EAAEA;IACb,CAAC,CAAC;EACJ;EAEAK,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB;MACA,oBACEN,OAAA;QAAKc,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEf,OAAA;UAAKc,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5Ef,OAAA;YAAKc,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBf,OAAA;cAAKc,SAAS,EAAC,gCAAgC;cAACE,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAH,QAAA,eACnGf,OAAA;gBAAMmB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1B,OAAA;YAAIc,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAEzD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1B,OAAA;YAAGc,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ1B,OAAA;YAAKc,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBf,OAAA;cACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxChB,SAAS,EAAC,0FAA0F;cAAAC,QAAA,EACrG;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1B,OAAA;cACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,GAAI;cAC1CjB,SAAS,EAAC,2FAA2F;cAAAC,QAAA,EACtG;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EACLM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAAC7B,KAAK,CAACE,KAAK,iBACzDP,OAAA;YAASc,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBACjCf,OAAA;cAASc,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAE1D;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACV1B,OAAA;cAAKc,SAAS,EAAC,oDAAoD;cAAAC,QAAA,GAChE,IAAI,CAACV,KAAK,CAACE,KAAK,CAAC4B,QAAQ,CAAC,CAAC,EAC3B,IAAI,CAAC9B,KAAK,CAACG,SAAS,CAAC4B,cAAc;YAAA;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACtB,KAAK,CAACW,QAAQ;EAC5B;AACF;AAEA,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}