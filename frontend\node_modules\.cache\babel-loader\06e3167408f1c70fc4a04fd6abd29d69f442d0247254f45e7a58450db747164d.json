{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\ProductDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Star, ShoppingCart, Heart, Share2, Minus, Plus, Truck, Shield, RotateCcw } from 'react-feather';\nimport { getProduct } from '../store/slices/productSlice';\nimport { addToCart } from '../store/slices/cartSlice';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProductDetail = () => {\n  _s();\n  var _product$images2, _product$images3, _product$stock2, _product$stock3, _product$stock4, _product$stock5, _product$category, _product$rating;\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [quantity, setQuantity] = useState(1);\n  const [activeTab, setActiveTab] = useState('description');\n  const {\n    product,\n    isLoading,\n    error\n  } = useSelector(state => state.products);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    if (id) {\n      dispatch(getProduct(id));\n    }\n  }, [dispatch, id]);\n  useEffect(() => {\n    var _product$images;\n    if ((product === null || product === void 0 ? void 0 : (_product$images = product.images) === null || _product$images === void 0 ? void 0 : _product$images.length) > 0) {\n      setSelectedImage(0);\n    }\n  }, [product]);\n  const handleAddToCart = () => {\n    if (!product) return;\n    dispatch(addToCart({\n      productId: product._id,\n      quantity: quantity\n    }));\n    toast.success(`${quantity} ${product.name}(s) added to cart!`);\n  };\n  const handleQuantityChange = change => {\n    var _product$stock;\n    const newQuantity = quantity + change;\n    if (newQuantity >= 1 && newQuantity <= ((product === null || product === void 0 ? void 0 : (_product$stock = product.stock) === null || _product$stock === void 0 ? void 0 : _product$stock.quantity) || 0)) {\n      setQuantity(newQuantity);\n    }\n  };\n  const handleWishlist = () => {\n    // TODO: Implement wishlist functionality\n    toast.success('Added to wishlist!');\n  };\n  const handleShare = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: product === null || product === void 0 ? void 0 : product.name,\n        text: product === null || product === void 0 ? void 0 : product.description,\n        url: window.location.href\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      toast.success('Product link copied to clipboard!');\n    }\n  };\n  const renderStars = rating => {\n    return [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(Star, {\n      size: 20,\n      className: `${index < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this));\n  };\n  const formatPrice = price => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8\",\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !product) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4 py-8 text-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-900 mb-4\",\n        children: \"Product Not Found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600 mb-8\",\n        children: \"The product you're looking for doesn't exist or has been removed.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/products'),\n        className: \"btn-primary\",\n        children: \"Browse Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container mx-auto px-4 py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex items-center space-x-2 text-sm text-gray-600 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/'),\n        className: \"hover:text-green-600\",\n        children: \"Home\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => navigate('/products'),\n        className: \"hover:text-green-600\",\n        children: \"Products\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"/\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"text-gray-900\",\n        children: product.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"aspect-square bg-gray-100 rounded-lg overflow-hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: ((_product$images2 = product.images) === null || _product$images2 === void 0 ? void 0 : _product$images2[selectedImage]) || '/api/placeholder/600/600',\n            alt: product.name,\n            className: \"w-full h-full object-cover\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), ((_product$images3 = product.images) === null || _product$images3 === void 0 ? void 0 : _product$images3.length) > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-2 overflow-x-auto\",\n          children: product.images.map((image, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedImage(index),\n            className: `flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${selectedImage === index ? 'border-green-500' : 'border-gray-200'}`,\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: image,\n              alt: `${product.name} ${index + 1}`,\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: product.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [renderStars(product.rating || 0), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600\",\n                children: [\"(\", product.numReviews || 0, \" reviews)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `px-2 py-1 rounded-full text-xs font-semibold ${(((_product$stock2 = product.stock) === null || _product$stock2 === void 0 ? void 0 : _product$stock2.quantity) || 0) > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n              children: (((_product$stock3 = product.stock) === null || _product$stock3 === void 0 ? void 0 : _product$stock3.quantity) || 0) > 0 ? 'In Stock' : 'Out of Stock'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-gray-500 line-through\",\n              children: formatPrice(product.originalPrice)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-3xl font-bold text-green-600\",\n              children: formatPrice(product.price)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-red-100 text-red-800 px-2 py-1 rounded-md text-sm font-semibold\",\n              children: [Math.round((product.originalPrice - product.price) / product.originalPrice * 100), \"% OFF\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-gray-700\",\n              children: \"Quantity:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center border border-gray-300 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(-1),\n                disabled: quantity <= 1,\n                className: \"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(Minus, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-4 py-2 font-medium\",\n                children: quantity\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleQuantityChange(1),\n                disabled: quantity >= (((_product$stock4 = product.stock) === null || _product$stock4 === void 0 ? void 0 : _product$stock4.quantity) || 0),\n                className: \"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\",\n                children: /*#__PURE__*/_jsxDEV(Plus, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-gray-500\",\n              children: [((_product$stock5 = product.stock) === null || _product$stock5 === void 0 ? void 0 : _product$stock5.quantity) || 0, \" available\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddToCart,\n              disabled: product.stock === 0,\n              className: \"flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n              children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n                size: 20,\n                className: \"mr-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 17\n              }, this), \"Add to Cart\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleWishlist,\n              className: \"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Heart, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleShare,\n              className: \"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(Share2, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border-t pt-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 sm:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Truck, {\n                className: \"text-green-600\",\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Free Delivery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"On orders over $50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Shield, {\n                className: \"text-green-600\",\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Quality Guarantee\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"Fresh products\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(RotateCcw, {\n                className: \"text-green-600\",\n                size: 24\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"font-medium text-sm\",\n                  children: \"Easy Returns\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-500\",\n                  children: \"30-day policy\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm border border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex space-x-8 px-6\",\n          children: ['description', 'specifications', 'reviews'].map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab),\n            className: `py-4 px-1 border-b-2 font-medium text-sm capitalize ${activeTab === tab ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`,\n            children: tab\n          }, tab, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: [activeTab === 'description' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"prose max-w-none\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-700 leading-relaxed\",\n            children: product.description || 'No description available.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 15\n          }, this), product.features && product.features.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold mb-3\",\n              children: \"Key Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"list-disc list-inside space-y-2\",\n              children: product.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"text-gray-700\",\n                children: feature\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this), activeTab === 'specifications' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold\",\n            children: \"Product Specifications\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Category\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 323,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: ((_product$category = product.category) === null || _product$category === void 0 ? void 0 : _product$category.name) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Brand\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: product.brand || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Weight\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: product.weight || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"SKU\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: product.sku || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Stock\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: [product.stock, \" units\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 342,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between py-2 border-b border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-medium text-gray-700\",\n                  children: \"Expiry Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-gray-600\",\n                  children: product.expiryDate ? new Date(product.expiryDate).toLocaleDateString() : 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 346,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 344,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 13\n        }, this), activeTab === 'reviews' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: \"Customer Reviews\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-secondary\",\n              children: \"Write a Review\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-50 rounded-lg p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: ((_product$rating = product.rating) === null || _product$rating === void 0 ? void 0 : _product$rating.toFixed(1)) || '0.0'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center mb-2\",\n                children: renderStars(product.rating || 0)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600\",\n                children: [\"Based on \", product.numReviews || 0, \" reviews\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 15\n          }, this), product.reviews && product.reviews.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: product.reviews.map((review, index) => {\n              var _review$user;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border-b border-gray-200 pb-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between mb-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900\",\n                      children: ((_review$user = review.user) === null || _review$user === void 0 ? void 0 : _review$user.name) || 'Anonymous'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 382,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: renderStars(review.rating)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-sm text-gray-500\",\n                    children: new Date(review.createdAt).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-700\",\n                  children: review.comment\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 23\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: \"No reviews yet. Be the first to review this product!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetail, \"C0sAgGgdnIzc8tHdwYfrDGdTTQc=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector, useSelector];\n});\n_c = ProductDetail;\nexport default ProductDetail;\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "Star", "ShoppingCart", "Heart", "Share2", "Minus", "Plus", "Truck", "Shield", "RotateCcw", "getProduct", "addToCart", "LoadingSpinner", "toast", "jsxDEV", "_jsxDEV", "ProductDetail", "_s", "_product$images2", "_product$images3", "_product$stock2", "_product$stock3", "_product$stock4", "_product$stock5", "_product$category", "_product$rating", "id", "navigate", "dispatch", "selectedImage", "setSelectedImage", "quantity", "setQuantity", "activeTab", "setActiveTab", "product", "isLoading", "error", "state", "products", "user", "auth", "_product$images", "images", "length", "handleAddToCart", "productId", "_id", "success", "name", "handleQuantityChange", "change", "_product$stock", "newQuantity", "stock", "handleWishlist", "handleShare", "navigator", "share", "title", "text", "description", "url", "window", "location", "href", "clipboard", "writeText", "renderStars", "rating", "Array", "map", "_", "index", "size", "className", "Math", "floor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "formatPrice", "price", "Intl", "NumberFormat", "style", "currency", "format", "children", "onClick", "src", "alt", "image", "numReviews", "originalPrice", "round", "disabled", "tab", "features", "feature", "category", "brand", "weight", "sku", "expiryDate", "Date", "toLocaleDateString", "toFixed", "reviews", "review", "_review$user", "createdAt", "comment", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/ProductDetail.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Star, ShoppingCart, Heart, Share2, Minus, Plus, Truck, Shield, RotateCcw } from 'react-feather';\nimport { getProduct } from '../store/slices/productSlice';\nimport { addToCart } from '../store/slices/cartSlice';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\n\nconst ProductDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n\n  const [selectedImage, setSelectedImage] = useState(0);\n  const [quantity, setQuantity] = useState(1);\n  const [activeTab, setActiveTab] = useState('description');\n\n  const { product, isLoading, error } = useSelector(state => state.products);\n  const { user } = useSelector(state => state.auth);\n\n  useEffect(() => {\n    if (id) {\n      dispatch(getProduct(id));\n    }\n  }, [dispatch, id]);\n\n  useEffect(() => {\n    if (product?.images?.length > 0) {\n      setSelectedImage(0);\n    }\n  }, [product]);\n\n  const handleAddToCart = () => {\n    if (!product) return;\n\n    dispatch(addToCart({\n      productId: product._id,\n      quantity: quantity\n    }));\n\n    toast.success(`${quantity} ${product.name}(s) added to cart!`);\n  };\n\n  const handleQuantityChange = (change) => {\n    const newQuantity = quantity + change;\n    if (newQuantity >= 1 && newQuantity <= (product?.stock?.quantity || 0)) {\n      setQuantity(newQuantity);\n    }\n  };\n\n  const handleWishlist = () => {\n    // TODO: Implement wishlist functionality\n    toast.success('Added to wishlist!');\n  };\n\n  const handleShare = () => {\n    if (navigator.share) {\n      navigator.share({\n        title: product?.name,\n        text: product?.description,\n        url: window.location.href,\n      });\n    } else {\n      navigator.clipboard.writeText(window.location.href);\n      toast.success('Product link copied to clipboard!');\n    }\n  };\n\n  const renderStars = (rating) => {\n    return [...Array(5)].map((_, index) => (\n      <Star\n        key={index}\n        size={20}\n        className={`${\n          index < Math.floor(rating)\n            ? 'text-yellow-400 fill-current'\n            : 'text-gray-300'\n        }`}\n      />\n    ));\n  };\n\n  const formatPrice = (price) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD'\n    }).format(price);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <LoadingSpinner />\n      </div>\n    );\n  }\n\n  if (error || !product) {\n    return (\n      <div className=\"container mx-auto px-4 py-8 text-center\">\n        <h1 className=\"text-2xl font-bold text-gray-900 mb-4\">Product Not Found</h1>\n        <p className=\"text-gray-600 mb-8\">The product you're looking for doesn't exist or has been removed.</p>\n        <button\n          onClick={() => navigate('/products')}\n          className=\"btn-primary\"\n        >\n          Browse Products\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Breadcrumb */}\n      <nav className=\"flex items-center space-x-2 text-sm text-gray-600 mb-8\">\n        <button onClick={() => navigate('/')} className=\"hover:text-green-600\">\n          Home\n        </button>\n        <span>/</span>\n        <button onClick={() => navigate('/products')} className=\"hover:text-green-600\">\n          Products\n        </button>\n        <span>/</span>\n        <span className=\"text-gray-900\">{product.name}</span>\n      </nav>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12\">\n        {/* Product Images */}\n        <div className=\"space-y-4\">\n          <div className=\"aspect-square bg-gray-100 rounded-lg overflow-hidden\">\n            <img\n              src={product.images?.[selectedImage] || '/api/placeholder/600/600'}\n              alt={product.name}\n              className=\"w-full h-full object-cover\"\n            />\n          </div>\n\n          {product.images?.length > 1 && (\n            <div className=\"flex space-x-2 overflow-x-auto\">\n              {product.images.map((image, index) => (\n                <button\n                  key={index}\n                  onClick={() => setSelectedImage(index)}\n                  className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${\n                    selectedImage === index ? 'border-green-500' : 'border-gray-200'\n                  }`}\n                >\n                  <img\n                    src={image}\n                    alt={`${product.name} ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </button>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* Product Info */}\n        <div className=\"space-y-6\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{product.name}</h1>\n            <div className=\"flex items-center space-x-4 mb-4\">\n              <div className=\"flex items-center\">\n                {renderStars(product.rating || 0)}\n                <span className=\"ml-2 text-sm text-gray-600\">\n                  ({product.numReviews || 0} reviews)\n                </span>\n              </div>\n              <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                (product.stock?.quantity || 0) > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'\n              }`}>\n                {(product.stock?.quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'}\n              </span>\n            </div>\n          </div>\n\n          {/* Price */}\n          <div className=\"space-y-2\">\n            <div className=\"flex items-center space-x-4\">\n              {product.originalPrice && product.originalPrice > product.price && (\n                <span className=\"text-2xl text-gray-500 line-through\">\n                  {formatPrice(product.originalPrice)}\n                </span>\n              )}\n              <span className=\"text-3xl font-bold text-green-600\">\n                {formatPrice(product.price)}\n              </span>\n              {product.originalPrice && product.originalPrice > product.price && (\n                <span className=\"bg-red-100 text-red-800 px-2 py-1 rounded-md text-sm font-semibold\">\n                  {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF\n                </span>\n              )}\n            </div>\n          </div>\n\n          {/* Quantity and Add to Cart */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm font-medium text-gray-700\">Quantity:</span>\n              <div className=\"flex items-center border border-gray-300 rounded-lg\">\n                <button\n                  onClick={() => handleQuantityChange(-1)}\n                  disabled={quantity <= 1}\n                  className=\"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <Minus size={16} />\n                </button>\n                <span className=\"px-4 py-2 font-medium\">{quantity}</span>\n                <button\n                  onClick={() => handleQuantityChange(1)}\n                  disabled={quantity >= (product.stock?.quantity || 0)}\n                  className=\"p-2 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  <Plus size={16} />\n                </button>\n              </div>\n              <span className=\"text-sm text-gray-500\">\n                {product.stock?.quantity || 0} available\n              </span>\n            </div>\n\n            <div className=\"flex space-x-4\">\n              <button\n                onClick={handleAddToCart}\n                disabled={product.stock === 0}\n                className=\"flex-1 btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                <ShoppingCart size={20} className=\"mr-2\" />\n                Add to Cart\n              </button>\n              <button\n                onClick={handleWishlist}\n                className=\"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                <Heart size={20} />\n              </button>\n              <button\n                onClick={handleShare}\n                className=\"p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                <Share2 size={20} />\n              </button>\n            </div>\n          </div>\n\n          {/* Features */}\n          <div className=\"border-t pt-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n              <div className=\"flex items-center space-x-3\">\n                <Truck className=\"text-green-600\" size={24} />\n                <div>\n                  <div className=\"font-medium text-sm\">Free Delivery</div>\n                  <div className=\"text-xs text-gray-500\">On orders over $50</div>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <Shield className=\"text-green-600\" size={24} />\n                <div>\n                  <div className=\"font-medium text-sm\">Quality Guarantee</div>\n                  <div className=\"text-xs text-gray-500\">Fresh products</div>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <RotateCcw className=\"text-green-600\" size={24} />\n                <div>\n                  <div className=\"font-medium text-sm\">Easy Returns</div>\n                  <div className=\"text-xs text-gray-500\">30-day policy</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Product Details Tabs */}\n      <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n        <div className=\"border-b border-gray-200\">\n          <nav className=\"flex space-x-8 px-6\">\n            {['description', 'specifications', 'reviews'].map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${\n                  activeTab === tab\n                    ? 'border-green-500 text-green-600'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                }`}\n              >\n                {tab}\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {activeTab === 'description' && (\n            <div className=\"prose max-w-none\">\n              <p className=\"text-gray-700 leading-relaxed\">\n                {product.description || 'No description available.'}\n              </p>\n              {product.features && product.features.length > 0 && (\n                <div className=\"mt-6\">\n                  <h3 className=\"text-lg font-semibold mb-3\">Key Features</h3>\n                  <ul className=\"list-disc list-inside space-y-2\">\n                    {product.features.map((feature, index) => (\n                      <li key={index} className=\"text-gray-700\">{feature}</li>\n                    ))}\n                  </ul>\n                </div>\n              )}\n            </div>\n          )}\n\n          {activeTab === 'specifications' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold\">Product Specifications</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">Category</span>\n                    <span className=\"text-gray-600\">{product.category?.name || 'N/A'}</span>\n                  </div>\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">Brand</span>\n                    <span className=\"text-gray-600\">{product.brand || 'N/A'}</span>\n                  </div>\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">Weight</span>\n                    <span className=\"text-gray-600\">{product.weight || 'N/A'}</span>\n                  </div>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">SKU</span>\n                    <span className=\"text-gray-600\">{product.sku || 'N/A'}</span>\n                  </div>\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">Stock</span>\n                    <span className=\"text-gray-600\">{product.stock} units</span>\n                  </div>\n                  <div className=\"flex justify-between py-2 border-b border-gray-100\">\n                    <span className=\"font-medium text-gray-700\">Expiry Date</span>\n                    <span className=\"text-gray-600\">{product.expiryDate ? new Date(product.expiryDate).toLocaleDateString() : 'N/A'}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'reviews' && (\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold\">Customer Reviews</h3>\n                <button className=\"btn-secondary\">\n                  Write a Review\n                </button>\n              </div>\n\n              <div className=\"bg-gray-50 rounded-lg p-6\">\n                <div className=\"text-center\">\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">\n                    {product.rating?.toFixed(1) || '0.0'}\n                  </div>\n                  <div className=\"flex items-center justify-center mb-2\">\n                    {renderStars(product.rating || 0)}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">\n                    Based on {product.numReviews || 0} reviews\n                  </div>\n                </div>\n              </div>\n\n              {product.reviews && product.reviews.length > 0 ? (\n                <div className=\"space-y-4\">\n                  {product.reviews.map((review, index) => (\n                    <div key={index} className=\"border-b border-gray-200 pb-4\">\n                      <div className=\"flex items-center justify-between mb-2\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"font-medium text-gray-900\">{review.user?.name || 'Anonymous'}</span>\n                          <div className=\"flex items-center\">\n                            {renderStars(review.rating)}\n                          </div>\n                        </div>\n                        <span className=\"text-sm text-gray-500\">\n                          {new Date(review.createdAt).toLocaleDateString()}\n                        </span>\n                      </div>\n                      <p className=\"text-gray-700\">{review.comment}</p>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  No reviews yet. Be the first to review this product!\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ProductDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,IAAI,EAAEC,YAAY,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACxG,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,eAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAG7B,SAAS,CAAC,CAAC;EAC1B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAC9B,MAAM8B,QAAQ,GAAG7B,WAAW,CAAC,CAAC;EAE9B,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACoC,QAAQ,EAAEC,WAAW,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAC3C,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAGvC,QAAQ,CAAC,aAAa,CAAC;EAEzD,MAAM;IAAEwC,OAAO;IAAEC,SAAS;IAAEC;EAAM,CAAC,GAAGrC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;EAC1E,MAAM;IAAEC;EAAK,CAAC,GAAGxC,WAAW,CAACsC,KAAK,IAAIA,KAAK,CAACG,IAAI,CAAC;EAEjD7C,SAAS,CAAC,MAAM;IACd,IAAI8B,EAAE,EAAE;MACNE,QAAQ,CAAClB,UAAU,CAACgB,EAAE,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACE,QAAQ,EAAEF,EAAE,CAAC,CAAC;EAElB9B,SAAS,CAAC,MAAM;IAAA,IAAA8C,eAAA;IACd,IAAI,CAAAP,OAAO,aAAPA,OAAO,wBAAAO,eAAA,GAAPP,OAAO,CAAEQ,MAAM,cAAAD,eAAA,uBAAfA,eAAA,CAAiBE,MAAM,IAAG,CAAC,EAAE;MAC/Bd,gBAAgB,CAAC,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACK,OAAO,CAAC,CAAC;EAEb,MAAMU,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACV,OAAO,EAAE;IAEdP,QAAQ,CAACjB,SAAS,CAAC;MACjBmC,SAAS,EAAEX,OAAO,CAACY,GAAG;MACtBhB,QAAQ,EAAEA;IACZ,CAAC,CAAC,CAAC;IAEHlB,KAAK,CAACmC,OAAO,CAAC,GAAGjB,QAAQ,IAAII,OAAO,CAACc,IAAI,oBAAoB,CAAC;EAChE,CAAC;EAED,MAAMC,oBAAoB,GAAIC,MAAM,IAAK;IAAA,IAAAC,cAAA;IACvC,MAAMC,WAAW,GAAGtB,QAAQ,GAAGoB,MAAM;IACrC,IAAIE,WAAW,IAAI,CAAC,IAAIA,WAAW,KAAK,CAAAlB,OAAO,aAAPA,OAAO,wBAAAiB,cAAA,GAAPjB,OAAO,CAAEmB,KAAK,cAAAF,cAAA,uBAAdA,cAAA,CAAgBrB,QAAQ,KAAI,CAAC,CAAC,EAAE;MACtEC,WAAW,CAACqB,WAAW,CAAC;IAC1B;EACF,CAAC;EAED,MAAME,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA1C,KAAK,CAACmC,OAAO,CAAC,oBAAoB,CAAC;EACrC,CAAC;EAED,MAAMQ,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIC,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAExB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEc,IAAI;QACpBW,IAAI,EAAEzB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0B,WAAW;QAC1BC,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLR,SAAS,CAACS,SAAS,CAACC,SAAS,CAACJ,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC;MACnDpD,KAAK,CAACmC,OAAO,CAAC,mCAAmC,CAAC;IACpD;EACF,CAAC;EAED,MAAMoB,WAAW,GAAIC,MAAM,IAAK;IAC9B,OAAO,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAChC1D,OAAA,CAACd,IAAI;MAEHyE,IAAI,EAAE,EAAG;MACTC,SAAS,EAAE,GACTF,KAAK,GAAGG,IAAI,CAACC,KAAK,CAACR,MAAM,CAAC,GACtB,8BAA8B,GAC9B,eAAe;IAClB,GANEI,KAAK;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOX,CACF,CAAC;EACJ,CAAC;EAED,MAAMC,WAAW,GAAIC,KAAK,IAAK;IAC7B,OAAO,IAAIC,IAAI,CAACC,YAAY,CAAC,OAAO,EAAE;MACpCC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE;IACZ,CAAC,CAAC,CAACC,MAAM,CAACL,KAAK,CAAC;EAClB,CAAC;EAED,IAAI/C,SAAS,EAAE;IACb,oBACErB,OAAA;MAAK4D,SAAS,EAAC,6BAA6B;MAAAc,QAAA,eAC1C1E,OAAA,CAACH,cAAc;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAEV;EAEA,IAAI5C,KAAK,IAAI,CAACF,OAAO,EAAE;IACrB,oBACEpB,OAAA;MAAK4D,SAAS,EAAC,yCAAyC;MAAAc,QAAA,gBACtD1E,OAAA;QAAI4D,SAAS,EAAC,uCAAuC;QAAAc,QAAA,EAAC;MAAiB;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5ElE,OAAA;QAAG4D,SAAS,EAAC,oBAAoB;QAAAc,QAAA,EAAC;MAAiE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACvGlE,OAAA;QACE2E,OAAO,EAAEA,CAAA,KAAM/D,QAAQ,CAAC,WAAW,CAAE;QACrCgD,SAAS,EAAC,aAAa;QAAAc,QAAA,EACxB;MAED;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACElE,OAAA;IAAK4D,SAAS,EAAC,6BAA6B;IAAAc,QAAA,gBAE1C1E,OAAA;MAAK4D,SAAS,EAAC,wDAAwD;MAAAc,QAAA,gBACrE1E,OAAA;QAAQ2E,OAAO,EAAEA,CAAA,KAAM/D,QAAQ,CAAC,GAAG,CAAE;QAACgD,SAAS,EAAC,sBAAsB;QAAAc,QAAA,EAAC;MAEvE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QAAA0E,QAAA,EAAM;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACdlE,OAAA;QAAQ2E,OAAO,EAAEA,CAAA,KAAM/D,QAAQ,CAAC,WAAW,CAAE;QAACgD,SAAS,EAAC,sBAAsB;QAAAc,QAAA,EAAC;MAE/E;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlE,OAAA;QAAA0E,QAAA,EAAM;MAAC;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACdlE,OAAA;QAAM4D,SAAS,EAAC,eAAe;QAAAc,QAAA,EAAEtD,OAAO,CAACc;MAAI;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENlE,OAAA;MAAK4D,SAAS,EAAC,6CAA6C;MAAAc,QAAA,gBAE1D1E,OAAA;QAAK4D,SAAS,EAAC,WAAW;QAAAc,QAAA,gBACxB1E,OAAA;UAAK4D,SAAS,EAAC,sDAAsD;UAAAc,QAAA,eACnE1E,OAAA;YACE4E,GAAG,EAAE,EAAAzE,gBAAA,GAAAiB,OAAO,CAACQ,MAAM,cAAAzB,gBAAA,uBAAdA,gBAAA,CAAiBW,aAAa,CAAC,KAAI,0BAA2B;YACnE+D,GAAG,EAAEzD,OAAO,CAACc,IAAK;YAClB0B,SAAS,EAAC;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAEL,EAAA9D,gBAAA,GAAAgB,OAAO,CAACQ,MAAM,cAAAxB,gBAAA,uBAAdA,gBAAA,CAAgByB,MAAM,IAAG,CAAC,iBACzB7B,OAAA;UAAK4D,SAAS,EAAC,gCAAgC;UAAAc,QAAA,EAC5CtD,OAAO,CAACQ,MAAM,CAAC4B,GAAG,CAAC,CAACsB,KAAK,EAAEpB,KAAK,kBAC/B1D,OAAA;YAEE2E,OAAO,EAAEA,CAAA,KAAM5D,gBAAgB,CAAC2C,KAAK,CAAE;YACvCE,SAAS,EAAE,+DACT9C,aAAa,KAAK4C,KAAK,GAAG,kBAAkB,GAAG,iBAAiB,EAC/D;YAAAgB,QAAA,eAEH1E,OAAA;cACE4E,GAAG,EAAEE,KAAM;cACXD,GAAG,EAAE,GAAGzD,OAAO,CAACc,IAAI,IAAIwB,KAAK,GAAG,CAAC,EAAG;cACpCE,SAAS,EAAC;YAA4B;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC,GAVGR,KAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAWJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNlE,OAAA;QAAK4D,SAAS,EAAC,WAAW;QAAAc,QAAA,gBACxB1E,OAAA;UAAA0E,QAAA,gBACE1E,OAAA;YAAI4D,SAAS,EAAC,uCAAuC;YAAAc,QAAA,EAAEtD,OAAO,CAACc;UAAI;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzElE,OAAA;YAAK4D,SAAS,EAAC,kCAAkC;YAAAc,QAAA,gBAC/C1E,OAAA;cAAK4D,SAAS,EAAC,mBAAmB;cAAAc,QAAA,GAC/BrB,WAAW,CAACjC,OAAO,CAACkC,MAAM,IAAI,CAAC,CAAC,eACjCtD,OAAA;gBAAM4D,SAAS,EAAC,4BAA4B;gBAAAc,QAAA,GAAC,GAC1C,EAACtD,OAAO,CAAC2D,UAAU,IAAI,CAAC,EAAC,WAC5B;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNlE,OAAA;cAAM4D,SAAS,EAAE,gDACf,CAAC,EAAAvD,eAAA,GAAAe,OAAO,CAACmB,KAAK,cAAAlC,eAAA,uBAAbA,eAAA,CAAeW,QAAQ,KAAI,CAAC,IAAI,CAAC,GAAG,6BAA6B,GAAG,yBAAyB,EAC7F;cAAA0D,QAAA,EACA,CAAC,EAAApE,eAAA,GAAAc,OAAO,CAACmB,KAAK,cAAAjC,eAAA,uBAAbA,eAAA,CAAeU,QAAQ,KAAI,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG;YAAc;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAc,QAAA,eACxB1E,OAAA;YAAK4D,SAAS,EAAC,6BAA6B;YAAAc,QAAA,GACzCtD,OAAO,CAAC4D,aAAa,IAAI5D,OAAO,CAAC4D,aAAa,GAAG5D,OAAO,CAACgD,KAAK,iBAC7DpE,OAAA;cAAM4D,SAAS,EAAC,qCAAqC;cAAAc,QAAA,EAClDP,WAAW,CAAC/C,OAAO,CAAC4D,aAAa;YAAC;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CACP,eACDlE,OAAA;cAAM4D,SAAS,EAAC,mCAAmC;cAAAc,QAAA,EAChDP,WAAW,CAAC/C,OAAO,CAACgD,KAAK;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACN9C,OAAO,CAAC4D,aAAa,IAAI5D,OAAO,CAAC4D,aAAa,GAAG5D,OAAO,CAACgD,KAAK,iBAC7DpE,OAAA;cAAM4D,SAAS,EAAC,oEAAoE;cAAAc,QAAA,GACjFb,IAAI,CAACoB,KAAK,CAAE,CAAC7D,OAAO,CAAC4D,aAAa,GAAG5D,OAAO,CAACgD,KAAK,IAAIhD,OAAO,CAAC4D,aAAa,GAAI,GAAG,CAAC,EAAC,OACvF;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAc,QAAA,gBACxB1E,OAAA;YAAK4D,SAAS,EAAC,6BAA6B;YAAAc,QAAA,gBAC1C1E,OAAA;cAAM4D,SAAS,EAAC,mCAAmC;cAAAc,QAAA,EAAC;YAAS;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpElE,OAAA;cAAK4D,SAAS,EAAC,qDAAqD;cAAAc,QAAA,gBAClE1E,OAAA;gBACE2E,OAAO,EAAEA,CAAA,KAAMxC,oBAAoB,CAAC,CAAC,CAAC,CAAE;gBACxC+C,QAAQ,EAAElE,QAAQ,IAAI,CAAE;gBACxB4C,SAAS,EAAC,uEAAuE;gBAAAc,QAAA,eAEjF1E,OAAA,CAACV,KAAK;kBAACqE,IAAI,EAAE;gBAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACTlE,OAAA;gBAAM4D,SAAS,EAAC,uBAAuB;gBAAAc,QAAA,EAAE1D;cAAQ;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACzDlE,OAAA;gBACE2E,OAAO,EAAEA,CAAA,KAAMxC,oBAAoB,CAAC,CAAC,CAAE;gBACvC+C,QAAQ,EAAElE,QAAQ,KAAK,EAAAT,eAAA,GAAAa,OAAO,CAACmB,KAAK,cAAAhC,eAAA,uBAAbA,eAAA,CAAeS,QAAQ,KAAI,CAAC,CAAE;gBACrD4C,SAAS,EAAC,uEAAuE;gBAAAc,QAAA,eAEjF1E,OAAA,CAACT,IAAI;kBAACoE,IAAI,EAAE;gBAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eACNlE,OAAA;cAAM4D,SAAS,EAAC,uBAAuB;cAAAc,QAAA,GACpC,EAAAlE,eAAA,GAAAY,OAAO,CAACmB,KAAK,cAAA/B,eAAA,uBAAbA,eAAA,CAAeQ,QAAQ,KAAI,CAAC,EAAC,YAChC;YAAA;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENlE,OAAA;YAAK4D,SAAS,EAAC,gBAAgB;YAAAc,QAAA,gBAC7B1E,OAAA;cACE2E,OAAO,EAAE7C,eAAgB;cACzBoD,QAAQ,EAAE9D,OAAO,CAACmB,KAAK,KAAK,CAAE;cAC9BqB,SAAS,EAAC,oEAAoE;cAAAc,QAAA,gBAE9E1E,OAAA,CAACb,YAAY;gBAACwE,IAAI,EAAE,EAAG;gBAACC,SAAS,EAAC;cAAM;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAE7C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlE,OAAA;cACE2E,OAAO,EAAEnC,cAAe;cACxBoB,SAAS,EAAC,0EAA0E;cAAAc,QAAA,eAEpF1E,OAAA,CAACZ,KAAK;gBAACuE,IAAI,EAAE;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb,CAAC,eACTlE,OAAA;cACE2E,OAAO,EAAElC,WAAY;cACrBmB,SAAS,EAAC,0EAA0E;cAAAc,QAAA,eAEpF1E,OAAA,CAACX,MAAM;gBAACsE,IAAI,EAAE;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA;UAAK4D,SAAS,EAAC,eAAe;UAAAc,QAAA,eAC5B1E,OAAA;YAAK4D,SAAS,EAAC,uCAAuC;YAAAc,QAAA,gBACpD1E,OAAA;cAAK4D,SAAS,EAAC,6BAA6B;cAAAc,QAAA,gBAC1C1E,OAAA,CAACR,KAAK;gBAACoE,SAAS,EAAC,gBAAgB;gBAACD,IAAI,EAAE;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9ClE,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAK4D,SAAS,EAAC,qBAAqB;kBAAAc,QAAA,EAAC;gBAAa;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDlE,OAAA;kBAAK4D,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAkB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlE,OAAA;cAAK4D,SAAS,EAAC,6BAA6B;cAAAc,QAAA,gBAC1C1E,OAAA,CAACP,MAAM;gBAACmE,SAAS,EAAC,gBAAgB;gBAACD,IAAI,EAAE;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/ClE,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAK4D,SAAS,EAAC,qBAAqB;kBAAAc,QAAA,EAAC;gBAAiB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC5DlE,OAAA;kBAAK4D,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAc;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlE,OAAA;cAAK4D,SAAS,EAAC,6BAA6B;cAAAc,QAAA,gBAC1C1E,OAAA,CAACN,SAAS;gBAACkE,SAAS,EAAC,gBAAgB;gBAACD,IAAI,EAAE;cAAG;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClDlE,OAAA;gBAAA0E,QAAA,gBACE1E,OAAA;kBAAK4D,SAAS,EAAC,qBAAqB;kBAAAc,QAAA,EAAC;gBAAY;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvDlE,OAAA;kBAAK4D,SAAS,EAAC,uBAAuB;kBAAAc,QAAA,EAAC;gBAAa;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK4D,SAAS,EAAC,sDAAsD;MAAAc,QAAA,gBACnE1E,OAAA;QAAK4D,SAAS,EAAC,0BAA0B;QAAAc,QAAA,eACvC1E,OAAA;UAAK4D,SAAS,EAAC,qBAAqB;UAAAc,QAAA,EACjC,CAAC,aAAa,EAAE,gBAAgB,EAAE,SAAS,CAAC,CAAClB,GAAG,CAAE2B,GAAG,iBACpDnF,OAAA;YAEE2E,OAAO,EAAEA,CAAA,KAAMxD,YAAY,CAACgE,GAAG,CAAE;YACjCvB,SAAS,EAAE,uDACT1C,SAAS,KAAKiE,GAAG,GACb,iCAAiC,GACjC,4EAA4E,EAC/E;YAAAT,QAAA,EAEFS;UAAG,GARCA,GAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASF,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAK4D,SAAS,EAAC,KAAK;QAAAc,QAAA,GACjBxD,SAAS,KAAK,aAAa,iBAC1BlB,OAAA;UAAK4D,SAAS,EAAC,kBAAkB;UAAAc,QAAA,gBAC/B1E,OAAA;YAAG4D,SAAS,EAAC,+BAA+B;YAAAc,QAAA,EACzCtD,OAAO,CAAC0B,WAAW,IAAI;UAA2B;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,EACH9C,OAAO,CAACgE,QAAQ,IAAIhE,OAAO,CAACgE,QAAQ,CAACvD,MAAM,GAAG,CAAC,iBAC9C7B,OAAA;YAAK4D,SAAS,EAAC,MAAM;YAAAc,QAAA,gBACnB1E,OAAA;cAAI4D,SAAS,EAAC,4BAA4B;cAAAc,QAAA,EAAC;YAAY;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DlE,OAAA;cAAI4D,SAAS,EAAC,iCAAiC;cAAAc,QAAA,EAC5CtD,OAAO,CAACgE,QAAQ,CAAC5B,GAAG,CAAC,CAAC6B,OAAO,EAAE3B,KAAK,kBACnC1D,OAAA;gBAAgB4D,SAAS,EAAC,eAAe;gBAAAc,QAAA,EAAEW;cAAO,GAAzC3B,KAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CACxD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAEAhD,SAAS,KAAK,gBAAgB,iBAC7BlB,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAc,QAAA,gBACxB1E,OAAA;YAAI4D,SAAS,EAAC,uBAAuB;YAAAc,QAAA,EAAC;UAAsB;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjElE,OAAA;YAAK4D,SAAS,EAAC,uCAAuC;YAAAc,QAAA,gBACpD1E,OAAA;cAAK4D,SAAS,EAAC,WAAW;cAAAc,QAAA,gBACxB1E,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAQ;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3DlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAE,EAAAjE,iBAAA,GAAAW,OAAO,CAACkE,QAAQ,cAAA7E,iBAAA,uBAAhBA,iBAAA,CAAkByB,IAAI,KAAI;gBAAK;kBAAA6B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrE,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxDlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEtD,OAAO,CAACmE,KAAK,IAAI;gBAAK;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAM;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzDlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEtD,OAAO,CAACoE,MAAM,IAAI;gBAAK;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNlE,OAAA;cAAK4D,SAAS,EAAC,WAAW;cAAAc,QAAA,gBACxB1E,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEtD,OAAO,CAACqE,GAAG,IAAI;gBAAK;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAK;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxDlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,GAAEtD,OAAO,CAACmB,KAAK,EAAC,QAAM;gBAAA;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,oDAAoD;gBAAAc,QAAA,gBACjE1E,OAAA;kBAAM4D,SAAS,EAAC,2BAA2B;kBAAAc,QAAA,EAAC;gBAAW;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC9DlE,OAAA;kBAAM4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEtD,OAAO,CAACsE,UAAU,GAAG,IAAIC,IAAI,CAACvE,OAAO,CAACsE,UAAU,CAAC,CAACE,kBAAkB,CAAC,CAAC,GAAG;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEAhD,SAAS,KAAK,SAAS,iBACtBlB,OAAA;UAAK4D,SAAS,EAAC,WAAW;UAAAc,QAAA,gBACxB1E,OAAA;YAAK4D,SAAS,EAAC,mCAAmC;YAAAc,QAAA,gBAChD1E,OAAA;cAAI4D,SAAS,EAAC,uBAAuB;cAAAc,QAAA,EAAC;YAAgB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DlE,OAAA;cAAQ4D,SAAS,EAAC,eAAe;cAAAc,QAAA,EAAC;YAElC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAENlE,OAAA;YAAK4D,SAAS,EAAC,2BAA2B;YAAAc,QAAA,eACxC1E,OAAA;cAAK4D,SAAS,EAAC,aAAa;cAAAc,QAAA,gBAC1B1E,OAAA;gBAAK4D,SAAS,EAAC,uCAAuC;gBAAAc,QAAA,EACnD,EAAAhE,eAAA,GAAAU,OAAO,CAACkC,MAAM,cAAA5C,eAAA,uBAAdA,eAAA,CAAgBmF,OAAO,CAAC,CAAC,CAAC,KAAI;cAAK;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,uCAAuC;gBAAAc,QAAA,EACnDrB,WAAW,CAACjC,OAAO,CAACkC,MAAM,IAAI,CAAC;cAAC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACNlE,OAAA;gBAAK4D,SAAS,EAAC,uBAAuB;gBAAAc,QAAA,GAAC,WAC5B,EAACtD,OAAO,CAAC2D,UAAU,IAAI,CAAC,EAAC,UACpC;cAAA;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEL9C,OAAO,CAAC0E,OAAO,IAAI1E,OAAO,CAAC0E,OAAO,CAACjE,MAAM,GAAG,CAAC,gBAC5C7B,OAAA;YAAK4D,SAAS,EAAC,WAAW;YAAAc,QAAA,EACvBtD,OAAO,CAAC0E,OAAO,CAACtC,GAAG,CAAC,CAACuC,MAAM,EAAErC,KAAK;cAAA,IAAAsC,YAAA;cAAA,oBACjChG,OAAA;gBAAiB4D,SAAS,EAAC,+BAA+B;gBAAAc,QAAA,gBACxD1E,OAAA;kBAAK4D,SAAS,EAAC,wCAAwC;kBAAAc,QAAA,gBACrD1E,OAAA;oBAAK4D,SAAS,EAAC,6BAA6B;oBAAAc,QAAA,gBAC1C1E,OAAA;sBAAM4D,SAAS,EAAC,2BAA2B;sBAAAc,QAAA,EAAE,EAAAsB,YAAA,GAAAD,MAAM,CAACtE,IAAI,cAAAuE,YAAA,uBAAXA,YAAA,CAAa9D,IAAI,KAAI;oBAAW;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACrFlE,OAAA;sBAAK4D,SAAS,EAAC,mBAAmB;sBAAAc,QAAA,EAC/BrB,WAAW,CAAC0C,MAAM,CAACzC,MAAM;oBAAC;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNlE,OAAA;oBAAM4D,SAAS,EAAC,uBAAuB;oBAAAc,QAAA,EACpC,IAAIiB,IAAI,CAACI,MAAM,CAACE,SAAS,CAAC,CAACL,kBAAkB,CAAC;kBAAC;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNlE,OAAA;kBAAG4D,SAAS,EAAC,eAAe;kBAAAc,QAAA,EAAEqB,MAAM,CAACG;gBAAO;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA,GAZzCR,KAAK;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaV,CAAC;YAAA,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAENlE,OAAA;YAAK4D,SAAS,EAAC,gCAAgC;YAAAc,QAAA,EAAC;UAEhD;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA5YID,aAAa;EAAA,QACFnB,SAAS,EACPC,WAAW,EACXC,WAAW,EAMUC,WAAW,EAChCA,WAAW;AAAA;AAAAkH,EAAA,GAVxBlG,aAAa;AA8YnB,eAAeA,aAAa;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}