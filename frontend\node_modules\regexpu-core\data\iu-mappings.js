module.exports = new Map([
	[0x4B, 0x212A],
	[0x53, 0x17F],
	[0x6B, 0x212A],
	[0x73, 0x17F],
	[0xB5, 0x39C],
	[0xC5, 0x212B],
	[0xDF, 0x1E9E],
	[0xE5, 0x212B],
	[0x17F, 0x53],
	[0x1C4, 0x1C5],
	[0x1C5, 0x1C4],
	[0x1C7, 0x1C8],
	[0x1C8, 0x1C7],
	[0x1CA, 0x1CB],
	[0x1CB, 0x1CA],
	[0x1F1, 0x1F2],
	[0x1F2, 0x1F1],
	[0x345, 0x1FBE],
	[0x390, 0x1FD3],
	[0x392, 0x3D0],
	[0x395, 0x3F5],
	[0x398, 0x3F4],
	[0x399, 0x1FBE],
	[0x39A, 0x3F0],
	[0x39C, 0xB5],
	[0x3A0, 0x3D6],
	[0x3A1, 0x3F1],
	[0x3A3, 0x3C2],
	[0x3A6, 0x3D5],
	[0x3A9, 0x2126],
	[0x3B0, 0x1FE3],
	[0x3B8, 0x3F4],
	[0x3C2, 0x3A3],
	[0x3C9, 0x2126],
	[0x3D0, 0x392],
	[0x3D1, 0x3F4],
	[0x3D5, 0x3A6],
	[0x3D6, 0x3A0],
	[0x3F0, 0x39A],
	[0x3F1, 0x3A1],
	[0x3F4, [
		0x398,
		0x3D1,
		0x3B8
	]],
	[0x3F5, 0x395],
	[0x412, 0x1C80],
	[0x414, 0x1C81],
	[0x41E, 0x1C82],
	[0x421, 0x1C83],
	[0x422, 0x1C85],
	[0x42A, 0x1C86],
	[0x462, 0x1C87],
	[0x1C80, 0x412],
	[0x1C81, 0x414],
	[0x1C82, 0x41E],
	[0x1C83, 0x421],
	[0x1C84, 0x1C85],
	[0x1C85, [
		0x422,
		0x1C84
	]],
	[0x1C86, 0x42A],
	[0x1C87, 0x462],
	[0x1C88, 0xA64A],
	[0x1E60, 0x1E9B],
	[0x1E9B, 0x1E60],
	[0x1E9E, 0xDF],
	[0x1F80, 0x1F88],
	[0x1F81, 0x1F89],
	[0x1F82, 0x1F8A],
	[0x1F83, 0x1F8B],
	[0x1F84, 0x1F8C],
	[0x1F85, 0x1F8D],
	[0x1F86, 0x1F8E],
	[0x1F87, 0x1F8F],
	[0x1F88, 0x1F80],
	[0x1F89, 0x1F81],
	[0x1F8A, 0x1F82],
	[0x1F8B, 0x1F83],
	[0x1F8C, 0x1F84],
	[0x1F8D, 0x1F85],
	[0x1F8E, 0x1F86],
	[0x1F8F, 0x1F87],
	[0x1F90, 0x1F98],
	[0x1F91, 0x1F99],
	[0x1F92, 0x1F9A],
	[0x1F93, 0x1F9B],
	[0x1F94, 0x1F9C],
	[0x1F95, 0x1F9D],
	[0x1F96, 0x1F9E],
	[0x1F97, 0x1F9F],
	[0x1F98, 0x1F90],
	[0x1F99, 0x1F91],
	[0x1F9A, 0x1F92],
	[0x1F9B, 0x1F93],
	[0x1F9C, 0x1F94],
	[0x1F9D, 0x1F95],
	[0x1F9E, 0x1F96],
	[0x1F9F, 0x1F97],
	[0x1FA0, 0x1FA8],
	[0x1FA1, 0x1FA9],
	[0x1FA2, 0x1FAA],
	[0x1FA3, 0x1FAB],
	[0x1FA4, 0x1FAC],
	[0x1FA5, 0x1FAD],
	[0x1FA6, 0x1FAE],
	[0x1FA7, 0x1FAF],
	[0x1FA8, 0x1FA0],
	[0x1FA9, 0x1FA1],
	[0x1FAA, 0x1FA2],
	[0x1FAB, 0x1FA3],
	[0x1FAC, 0x1FA4],
	[0x1FAD, 0x1FA5],
	[0x1FAE, 0x1FA6],
	[0x1FAF, 0x1FA7],
	[0x1FB3, 0x1FBC],
	[0x1FBC, 0x1FB3],
	[0x1FBE, [
		0x345,
		0x399
	]],
	[0x1FC3, 0x1FCC],
	[0x1FCC, 0x1FC3],
	[0x1FD3, 0x390],
	[0x1FE3, 0x3B0],
	[0x1FF3, 0x1FFC],
	[0x1FFC, 0x1FF3],
	[0x2126, [
		0x3A9,
		0x3C9
	]],
	[0x212A, 0x4B],
	[0x212B, [
		0xC5,
		0xE5
	]],
	[0xA64A, 0x1C88],
	[0xFB05, 0xFB06],
	[0xFB06, 0xFB05],
	[0x10400, 0x10428],
	[0x10401, 0x10429],
	[0x10402, 0x1042A],
	[0x10403, 0x1042B],
	[0x10404, 0x1042C],
	[0x10405, 0x1042D],
	[0x10406, 0x1042E],
	[0x10407, 0x1042F],
	[0x10408, 0x10430],
	[0x10409, 0x10431],
	[0x1040A, 0x10432],
	[0x1040B, 0x10433],
	[0x1040C, 0x10434],
	[0x1040D, 0x10435],
	[0x1040E, 0x10436],
	[0x1040F, 0x10437],
	[0x10410, 0x10438],
	[0x10411, 0x10439],
	[0x10412, 0x1043A],
	[0x10413, 0x1043B],
	[0x10414, 0x1043C],
	[0x10415, 0x1043D],
	[0x10416, 0x1043E],
	[0x10417, 0x1043F],
	[0x10418, 0x10440],
	[0x10419, 0x10441],
	[0x1041A, 0x10442],
	[0x1041B, 0x10443],
	[0x1041C, 0x10444],
	[0x1041D, 0x10445],
	[0x1041E, 0x10446],
	[0x1041F, 0x10447],
	[0x10420, 0x10448],
	[0x10421, 0x10449],
	[0x10422, 0x1044A],
	[0x10423, 0x1044B],
	[0x10424, 0x1044C],
	[0x10425, 0x1044D],
	[0x10426, 0x1044E],
	[0x10427, 0x1044F],
	[0x10428, 0x10400],
	[0x10429, 0x10401],
	[0x1042A, 0x10402],
	[0x1042B, 0x10403],
	[0x1042C, 0x10404],
	[0x1042D, 0x10405],
	[0x1042E, 0x10406],
	[0x1042F, 0x10407],
	[0x10430, 0x10408],
	[0x10431, 0x10409],
	[0x10432, 0x1040A],
	[0x10433, 0x1040B],
	[0x10434, 0x1040C],
	[0x10435, 0x1040D],
	[0x10436, 0x1040E],
	[0x10437, 0x1040F],
	[0x10438, 0x10410],
	[0x10439, 0x10411],
	[0x1043A, 0x10412],
	[0x1043B, 0x10413],
	[0x1043C, 0x10414],
	[0x1043D, 0x10415],
	[0x1043E, 0x10416],
	[0x1043F, 0x10417],
	[0x10440, 0x10418],
	[0x10441, 0x10419],
	[0x10442, 0x1041A],
	[0x10443, 0x1041B],
	[0x10444, 0x1041C],
	[0x10445, 0x1041D],
	[0x10446, 0x1041E],
	[0x10447, 0x1041F],
	[0x10448, 0x10420],
	[0x10449, 0x10421],
	[0x1044A, 0x10422],
	[0x1044B, 0x10423],
	[0x1044C, 0x10424],
	[0x1044D, 0x10425],
	[0x1044E, 0x10426],
	[0x1044F, 0x10427],
	[0x104B0, 0x104D8],
	[0x104B1, 0x104D9],
	[0x104B2, 0x104DA],
	[0x104B3, 0x104DB],
	[0x104B4, 0x104DC],
	[0x104B5, 0x104DD],
	[0x104B6, 0x104DE],
	[0x104B7, 0x104DF],
	[0x104B8, 0x104E0],
	[0x104B9, 0x104E1],
	[0x104BA, 0x104E2],
	[0x104BB, 0x104E3],
	[0x104BC, 0x104E4],
	[0x104BD, 0x104E5],
	[0x104BE, 0x104E6],
	[0x104BF, 0x104E7],
	[0x104C0, 0x104E8],
	[0x104C1, 0x104E9],
	[0x104C2, 0x104EA],
	[0x104C3, 0x104EB],
	[0x104C4, 0x104EC],
	[0x104C5, 0x104ED],
	[0x104C6, 0x104EE],
	[0x104C7, 0x104EF],
	[0x104C8, 0x104F0],
	[0x104C9, 0x104F1],
	[0x104CA, 0x104F2],
	[0x104CB, 0x104F3],
	[0x104CC, 0x104F4],
	[0x104CD, 0x104F5],
	[0x104CE, 0x104F6],
	[0x104CF, 0x104F7],
	[0x104D0, 0x104F8],
	[0x104D1, 0x104F9],
	[0x104D2, 0x104FA],
	[0x104D3, 0x104FB],
	[0x104D8, 0x104B0],
	[0x104D9, 0x104B1],
	[0x104DA, 0x104B2],
	[0x104DB, 0x104B3],
	[0x104DC, 0x104B4],
	[0x104DD, 0x104B5],
	[0x104DE, 0x104B6],
	[0x104DF, 0x104B7],
	[0x104E0, 0x104B8],
	[0x104E1, 0x104B9],
	[0x104E2, 0x104BA],
	[0x104E3, 0x104BB],
	[0x104E4, 0x104BC],
	[0x104E5, 0x104BD],
	[0x104E6, 0x104BE],
	[0x104E7, 0x104BF],
	[0x104E8, 0x104C0],
	[0x104E9, 0x104C1],
	[0x104EA, 0x104C2],
	[0x104EB, 0x104C3],
	[0x104EC, 0x104C4],
	[0x104ED, 0x104C5],
	[0x104EE, 0x104C6],
	[0x104EF, 0x104C7],
	[0x104F0, 0x104C8],
	[0x104F1, 0x104C9],
	[0x104F2, 0x104CA],
	[0x104F3, 0x104CB],
	[0x104F4, 0x104CC],
	[0x104F5, 0x104CD],
	[0x104F6, 0x104CE],
	[0x104F7, 0x104CF],
	[0x104F8, 0x104D0],
	[0x104F9, 0x104D1],
	[0x104FA, 0x104D2],
	[0x104FB, 0x104D3],
	[0x10570, 0x10597],
	[0x10571, 0x10598],
	[0x10572, 0x10599],
	[0x10573, 0x1059A],
	[0x10574, 0x1059B],
	[0x10575, 0x1059C],
	[0x10576, 0x1059D],
	[0x10577, 0x1059E],
	[0x10578, 0x1059F],
	[0x10579, 0x105A0],
	[0x1057A, 0x105A1],
	[0x1057C, 0x105A3],
	[0x1057D, 0x105A4],
	[0x1057E, 0x105A5],
	[0x1057F, 0x105A6],
	[0x10580, 0x105A7],
	[0x10581, 0x105A8],
	[0x10582, 0x105A9],
	[0x10583, 0x105AA],
	[0x10584, 0x105AB],
	[0x10585, 0x105AC],
	[0x10586, 0x105AD],
	[0x10587, 0x105AE],
	[0x10588, 0x105AF],
	[0x10589, 0x105B0],
	[0x1058A, 0x105B1],
	[0x1058C, 0x105B3],
	[0x1058D, 0x105B4],
	[0x1058E, 0x105B5],
	[0x1058F, 0x105B6],
	[0x10590, 0x105B7],
	[0x10591, 0x105B8],
	[0x10592, 0x105B9],
	[0x10594, 0x105BB],
	[0x10595, 0x105BC],
	[0x10597, 0x10570],
	[0x10598, 0x10571],
	[0x10599, 0x10572],
	[0x1059A, 0x10573],
	[0x1059B, 0x10574],
	[0x1059C, 0x10575],
	[0x1059D, 0x10576],
	[0x1059E, 0x10577],
	[0x1059F, 0x10578],
	[0x105A0, 0x10579],
	[0x105A1, 0x1057A],
	[0x105A3, 0x1057C],
	[0x105A4, 0x1057D],
	[0x105A5, 0x1057E],
	[0x105A6, 0x1057F],
	[0x105A7, 0x10580],
	[0x105A8, 0x10581],
	[0x105A9, 0x10582],
	[0x105AA, 0x10583],
	[0x105AB, 0x10584],
	[0x105AC, 0x10585],
	[0x105AD, 0x10586],
	[0x105AE, 0x10587],
	[0x105AF, 0x10588],
	[0x105B0, 0x10589],
	[0x105B1, 0x1058A],
	[0x105B3, 0x1058C],
	[0x105B4, 0x1058D],
	[0x105B5, 0x1058E],
	[0x105B6, 0x1058F],
	[0x105B7, 0x10590],
	[0x105B8, 0x10591],
	[0x105B9, 0x10592],
	[0x105BB, 0x10594],
	[0x105BC, 0x10595],
	[0x10C80, 0x10CC0],
	[0x10C81, 0x10CC1],
	[0x10C82, 0x10CC2],
	[0x10C83, 0x10CC3],
	[0x10C84, 0x10CC4],
	[0x10C85, 0x10CC5],
	[0x10C86, 0x10CC6],
	[0x10C87, 0x10CC7],
	[0x10C88, 0x10CC8],
	[0x10C89, 0x10CC9],
	[0x10C8A, 0x10CCA],
	[0x10C8B, 0x10CCB],
	[0x10C8C, 0x10CCC],
	[0x10C8D, 0x10CCD],
	[0x10C8E, 0x10CCE],
	[0x10C8F, 0x10CCF],
	[0x10C90, 0x10CD0],
	[0x10C91, 0x10CD1],
	[0x10C92, 0x10CD2],
	[0x10C93, 0x10CD3],
	[0x10C94, 0x10CD4],
	[0x10C95, 0x10CD5],
	[0x10C96, 0x10CD6],
	[0x10C97, 0x10CD7],
	[0x10C98, 0x10CD8],
	[0x10C99, 0x10CD9],
	[0x10C9A, 0x10CDA],
	[0x10C9B, 0x10CDB],
	[0x10C9C, 0x10CDC],
	[0x10C9D, 0x10CDD],
	[0x10C9E, 0x10CDE],
	[0x10C9F, 0x10CDF],
	[0x10CA0, 0x10CE0],
	[0x10CA1, 0x10CE1],
	[0x10CA2, 0x10CE2],
	[0x10CA3, 0x10CE3],
	[0x10CA4, 0x10CE4],
	[0x10CA5, 0x10CE5],
	[0x10CA6, 0x10CE6],
	[0x10CA7, 0x10CE7],
	[0x10CA8, 0x10CE8],
	[0x10CA9, 0x10CE9],
	[0x10CAA, 0x10CEA],
	[0x10CAB, 0x10CEB],
	[0x10CAC, 0x10CEC],
	[0x10CAD, 0x10CED],
	[0x10CAE, 0x10CEE],
	[0x10CAF, 0x10CEF],
	[0x10CB0, 0x10CF0],
	[0x10CB1, 0x10CF1],
	[0x10CB2, 0x10CF2],
	[0x10CC0, 0x10C80],
	[0x10CC1, 0x10C81],
	[0x10CC2, 0x10C82],
	[0x10CC3, 0x10C83],
	[0x10CC4, 0x10C84],
	[0x10CC5, 0x10C85],
	[0x10CC6, 0x10C86],
	[0x10CC7, 0x10C87],
	[0x10CC8, 0x10C88],
	[0x10CC9, 0x10C89],
	[0x10CCA, 0x10C8A],
	[0x10CCB, 0x10C8B],
	[0x10CCC, 0x10C8C],
	[0x10CCD, 0x10C8D],
	[0x10CCE, 0x10C8E],
	[0x10CCF, 0x10C8F],
	[0x10CD0, 0x10C90],
	[0x10CD1, 0x10C91],
	[0x10CD2, 0x10C92],
	[0x10CD3, 0x10C93],
	[0x10CD4, 0x10C94],
	[0x10CD5, 0x10C95],
	[0x10CD6, 0x10C96],
	[0x10CD7, 0x10C97],
	[0x10CD8, 0x10C98],
	[0x10CD9, 0x10C99],
	[0x10CDA, 0x10C9A],
	[0x10CDB, 0x10C9B],
	[0x10CDC, 0x10C9C],
	[0x10CDD, 0x10C9D],
	[0x10CDE, 0x10C9E],
	[0x10CDF, 0x10C9F],
	[0x10CE0, 0x10CA0],
	[0x10CE1, 0x10CA1],
	[0x10CE2, 0x10CA2],
	[0x10CE3, 0x10CA3],
	[0x10CE4, 0x10CA4],
	[0x10CE5, 0x10CA5],
	[0x10CE6, 0x10CA6],
	[0x10CE7, 0x10CA7],
	[0x10CE8, 0x10CA8],
	[0x10CE9, 0x10CA9],
	[0x10CEA, 0x10CAA],
	[0x10CEB, 0x10CAB],
	[0x10CEC, 0x10CAC],
	[0x10CED, 0x10CAD],
	[0x10CEE, 0x10CAE],
	[0x10CEF, 0x10CAF],
	[0x10CF0, 0x10CB0],
	[0x10CF1, 0x10CB1],
	[0x10CF2, 0x10CB2],
	[0x10D50, 0x10D70],
	[0x10D51, 0x10D71],
	[0x10D52, 0x10D72],
	[0x10D53, 0x10D73],
	[0x10D54, 0x10D74],
	[0x10D55, 0x10D75],
	[0x10D56, 0x10D76],
	[0x10D57, 0x10D77],
	[0x10D58, 0x10D78],
	[0x10D59, 0x10D79],
	[0x10D5A, 0x10D7A],
	[0x10D5B, 0x10D7B],
	[0x10D5C, 0x10D7C],
	[0x10D5D, 0x10D7D],
	[0x10D5E, 0x10D7E],
	[0x10D5F, 0x10D7F],
	[0x10D60, 0x10D80],
	[0x10D61, 0x10D81],
	[0x10D62, 0x10D82],
	[0x10D63, 0x10D83],
	[0x10D64, 0x10D84],
	[0x10D65, 0x10D85],
	[0x10D70, 0x10D50],
	[0x10D71, 0x10D51],
	[0x10D72, 0x10D52],
	[0x10D73, 0x10D53],
	[0x10D74, 0x10D54],
	[0x10D75, 0x10D55],
	[0x10D76, 0x10D56],
	[0x10D77, 0x10D57],
	[0x10D78, 0x10D58],
	[0x10D79, 0x10D59],
	[0x10D7A, 0x10D5A],
	[0x10D7B, 0x10D5B],
	[0x10D7C, 0x10D5C],
	[0x10D7D, 0x10D5D],
	[0x10D7E, 0x10D5E],
	[0x10D7F, 0x10D5F],
	[0x10D80, 0x10D60],
	[0x10D81, 0x10D61],
	[0x10D82, 0x10D62],
	[0x10D83, 0x10D63],
	[0x10D84, 0x10D64],
	[0x10D85, 0x10D65],
	[0x118A0, 0x118C0],
	[0x118A1, 0x118C1],
	[0x118A2, 0x118C2],
	[0x118A3, 0x118C3],
	[0x118A4, 0x118C4],
	[0x118A5, 0x118C5],
	[0x118A6, 0x118C6],
	[0x118A7, 0x118C7],
	[0x118A8, 0x118C8],
	[0x118A9, 0x118C9],
	[0x118AA, 0x118CA],
	[0x118AB, 0x118CB],
	[0x118AC, 0x118CC],
	[0x118AD, 0x118CD],
	[0x118AE, 0x118CE],
	[0x118AF, 0x118CF],
	[0x118B0, 0x118D0],
	[0x118B1, 0x118D1],
	[0x118B2, 0x118D2],
	[0x118B3, 0x118D3],
	[0x118B4, 0x118D4],
	[0x118B5, 0x118D5],
	[0x118B6, 0x118D6],
	[0x118B7, 0x118D7],
	[0x118B8, 0x118D8],
	[0x118B9, 0x118D9],
	[0x118BA, 0x118DA],
	[0x118BB, 0x118DB],
	[0x118BC, 0x118DC],
	[0x118BD, 0x118DD],
	[0x118BE, 0x118DE],
	[0x118BF, 0x118DF],
	[0x118C0, 0x118A0],
	[0x118C1, 0x118A1],
	[0x118C2, 0x118A2],
	[0x118C3, 0x118A3],
	[0x118C4, 0x118A4],
	[0x118C5, 0x118A5],
	[0x118C6, 0x118A6],
	[0x118C7, 0x118A7],
	[0x118C8, 0x118A8],
	[0x118C9, 0x118A9],
	[0x118CA, 0x118AA],
	[0x118CB, 0x118AB],
	[0x118CC, 0x118AC],
	[0x118CD, 0x118AD],
	[0x118CE, 0x118AE],
	[0x118CF, 0x118AF],
	[0x118D0, 0x118B0],
	[0x118D1, 0x118B1],
	[0x118D2, 0x118B2],
	[0x118D3, 0x118B3],
	[0x118D4, 0x118B4],
	[0x118D5, 0x118B5],
	[0x118D6, 0x118B6],
	[0x118D7, 0x118B7],
	[0x118D8, 0x118B8],
	[0x118D9, 0x118B9],
	[0x118DA, 0x118BA],
	[0x118DB, 0x118BB],
	[0x118DC, 0x118BC],
	[0x118DD, 0x118BD],
	[0x118DE, 0x118BE],
	[0x118DF, 0x118BF],
	[0x16E40, 0x16E60],
	[0x16E41, 0x16E61],
	[0x16E42, 0x16E62],
	[0x16E43, 0x16E63],
	[0x16E44, 0x16E64],
	[0x16E45, 0x16E65],
	[0x16E46, 0x16E66],
	[0x16E47, 0x16E67],
	[0x16E48, 0x16E68],
	[0x16E49, 0x16E69],
	[0x16E4A, 0x16E6A],
	[0x16E4B, 0x16E6B],
	[0x16E4C, 0x16E6C],
	[0x16E4D, 0x16E6D],
	[0x16E4E, 0x16E6E],
	[0x16E4F, 0x16E6F],
	[0x16E50, 0x16E70],
	[0x16E51, 0x16E71],
	[0x16E52, 0x16E72],
	[0x16E53, 0x16E73],
	[0x16E54, 0x16E74],
	[0x16E55, 0x16E75],
	[0x16E56, 0x16E76],
	[0x16E57, 0x16E77],
	[0x16E58, 0x16E78],
	[0x16E59, 0x16E79],
	[0x16E5A, 0x16E7A],
	[0x16E5B, 0x16E7B],
	[0x16E5C, 0x16E7C],
	[0x16E5D, 0x16E7D],
	[0x16E5E, 0x16E7E],
	[0x16E5F, 0x16E7F],
	[0x16E60, 0x16E40],
	[0x16E61, 0x16E41],
	[0x16E62, 0x16E42],
	[0x16E63, 0x16E43],
	[0x16E64, 0x16E44],
	[0x16E65, 0x16E45],
	[0x16E66, 0x16E46],
	[0x16E67, 0x16E47],
	[0x16E68, 0x16E48],
	[0x16E69, 0x16E49],
	[0x16E6A, 0x16E4A],
	[0x16E6B, 0x16E4B],
	[0x16E6C, 0x16E4C],
	[0x16E6D, 0x16E4D],
	[0x16E6E, 0x16E4E],
	[0x16E6F, 0x16E4F],
	[0x16E70, 0x16E50],
	[0x16E71, 0x16E51],
	[0x16E72, 0x16E52],
	[0x16E73, 0x16E53],
	[0x16E74, 0x16E54],
	[0x16E75, 0x16E55],
	[0x16E76, 0x16E56],
	[0x16E77, 0x16E57],
	[0x16E78, 0x16E58],
	[0x16E79, 0x16E59],
	[0x16E7A, 0x16E5A],
	[0x16E7B, 0x16E5B],
	[0x16E7C, 0x16E5C],
	[0x16E7D, 0x16E5D],
	[0x16E7E, 0x16E5E],
	[0x16E7F, 0x16E5F],
	[0x1E900, 0x1E922],
	[0x1E901, 0x1E923],
	[0x1E902, 0x1E924],
	[0x1E903, 0x1E925],
	[0x1E904, 0x1E926],
	[0x1E905, 0x1E927],
	[0x1E906, 0x1E928],
	[0x1E907, 0x1E929],
	[0x1E908, 0x1E92A],
	[0x1E909, 0x1E92B],
	[0x1E90A, 0x1E92C],
	[0x1E90B, 0x1E92D],
	[0x1E90C, 0x1E92E],
	[0x1E90D, 0x1E92F],
	[0x1E90E, 0x1E930],
	[0x1E90F, 0x1E931],
	[0x1E910, 0x1E932],
	[0x1E911, 0x1E933],
	[0x1E912, 0x1E934],
	[0x1E913, 0x1E935],
	[0x1E914, 0x1E936],
	[0x1E915, 0x1E937],
	[0x1E916, 0x1E938],
	[0x1E917, 0x1E939],
	[0x1E918, 0x1E93A],
	[0x1E919, 0x1E93B],
	[0x1E91A, 0x1E93C],
	[0x1E91B, 0x1E93D],
	[0x1E91C, 0x1E93E],
	[0x1E91D, 0x1E93F],
	[0x1E91E, 0x1E940],
	[0x1E91F, 0x1E941],
	[0x1E920, 0x1E942],
	[0x1E921, 0x1E943],
	[0x1E922, 0x1E900],
	[0x1E923, 0x1E901],
	[0x1E924, 0x1E902],
	[0x1E925, 0x1E903],
	[0x1E926, 0x1E904],
	[0x1E927, 0x1E905],
	[0x1E928, 0x1E906],
	[0x1E929, 0x1E907],
	[0x1E92A, 0x1E908],
	[0x1E92B, 0x1E909],
	[0x1E92C, 0x1E90A],
	[0x1E92D, 0x1E90B],
	[0x1E92E, 0x1E90C],
	[0x1E92F, 0x1E90D],
	[0x1E930, 0x1E90E],
	[0x1E931, 0x1E90F],
	[0x1E932, 0x1E910],
	[0x1E933, 0x1E911],
	[0x1E934, 0x1E912],
	[0x1E935, 0x1E913],
	[0x1E936, 0x1E914],
	[0x1E937, 0x1E915],
	[0x1E938, 0x1E916],
	[0x1E939, 0x1E917],
	[0x1E93A, 0x1E918],
	[0x1E93B, 0x1E919],
	[0x1E93C, 0x1E91A],
	[0x1E93D, 0x1E91B],
	[0x1E93E, 0x1E91C],
	[0x1E93F, 0x1E91D],
	[0x1E940, 0x1E91E],
	[0x1E941, 0x1E91F],
	[0x1E942, 0x1E920],
	[0x1E943, 0x1E921]
]);
