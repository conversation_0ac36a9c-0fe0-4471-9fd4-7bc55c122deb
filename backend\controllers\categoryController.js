const Category = require('../models/Category');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Get all categories
// @route   GET /api/categories
// @access  Public
const getCategories = asyncHandler(async (req, res, next) => {
  const { featured, parent, level } = req.query;

  const query = { isActive: true };

  // Filter by featured
  if (featured === 'true') {
    query.isFeatured = true;
  }

  // Filter by parent
  if (parent) {
    query.parent = parent === 'null' ? null : parent;
  }

  // Filter by level
  if (level !== undefined) {
    query.level = parseInt(level);
  }

  const categories = await Category.find(query)
    .populate('parent', 'name slug')
    .populate('subcategories', 'name slug level')
    .sort({ sortOrder: 1, name: 1 });

  res.status(200).json({
    success: true,
    count: categories.length,
    categories
  });
});

// @desc    Get category tree
// @route   GET /api/categories/tree
// @access  Public
const getCategoryTree = asyncHandler(async (req, res, next) => {
  const categoryTree = await Category.getCategoryTree();

  res.status(200).json({
    success: true,
    categories: categoryTree
  });
});

// @desc    Get featured categories
// @route   GET /api/categories/featured
// @access  Public
const getFeaturedCategories = asyncHandler(async (req, res, next) => {
  const categories = await Category.getFeatured();

  res.status(200).json({
    success: true,
    count: categories.length,
    categories
  });
});

// @desc    Get root categories
// @route   GET /api/categories/root
// @access  Public
const getRootCategories = asyncHandler(async (req, res, next) => {
  const categories = await Category.getRootCategories()
    .populate('subcategories', 'name slug level');

  res.status(200).json({
    success: true,
    count: categories.length,
    categories
  });
});

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Public
const getCategory = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id)
    .populate('parent', 'name slug')
    .populate('subcategories', 'name slug level productCount')
    .populate('createdBy', 'firstName lastName');

  if (!category || !category.isActive) {
    return next(new ErrorResponse('Category not found', 404));
  }

  res.status(200).json({
    success: true,
    category
  });
});

// @desc    Get category by slug
// @route   GET /api/categories/slug/:slug
// @access  Public
const getCategoryBySlug = asyncHandler(async (req, res, next) => {
  const category = await Category.findOne({ 
    slug: req.params.slug,
    isActive: true 
  })
    .populate('parent', 'name slug')
    .populate('subcategories', 'name slug level productCount')
    .populate('createdBy', 'firstName lastName');

  if (!category) {
    return next(new ErrorResponse('Category not found', 404));
  }

  res.status(200).json({
    success: true,
    category
  });
});

// @desc    Get category ancestors (breadcrumb)
// @route   GET /api/categories/:id/ancestors
// @access  Public
const getCategoryAncestors = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id);
  
  if (!category || !category.isActive) {
    return next(new ErrorResponse('Category not found', 404));
  }

  const ancestors = await category.getAncestors();

  res.status(200).json({
    success: true,
    ancestors: [...ancestors, category]
  });
});

// @desc    Get category descendants
// @route   GET /api/categories/:id/descendants
// @access  Public
const getCategoryDescendants = asyncHandler(async (req, res, next) => {
  const category = await Category.findById(req.params.id);
  
  if (!category || !category.isActive) {
    return next(new ErrorResponse('Category not found', 404));
  }

  const descendants = await category.getDescendants();

  res.status(200).json({
    success: true,
    count: descendants.length,
    descendants
  });
});

module.exports = {
  getCategories,
  getCategoryTree,
  getFeaturedCategories,
  getRootCategories,
  getCategory,
  getCategoryBySlug,
  getCategoryAncestors,
  getCategoryDescendants
};
