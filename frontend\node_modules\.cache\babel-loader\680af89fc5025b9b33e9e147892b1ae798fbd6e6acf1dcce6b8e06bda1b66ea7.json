{"ast": null, "code": "function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nimport React, { forwardRef } from 'react';\nimport PropTypes from 'prop-types';\nvar Sunset = forwardRef(function (_ref, ref) {\n  var _ref$color = _ref.color,\n    color = _ref$color === void 0 ? 'currentColor' : _ref$color,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 24 : _ref$size,\n    rest = _objectWithoutProperties(_ref, [\"color\", \"size\"]);\n  return /*#__PURE__*/React.createElement(\"svg\", _extends({\n    ref: ref,\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: size,\n    height: size,\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: color,\n    strokeWidth: \"2\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n  }, rest), /*#__PURE__*/React.createElement(\"path\", {\n    d: \"M17 18a5 5 0 0 0-10 0\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"12\",\n    y1: \"9\",\n    x2: \"12\",\n    y2: \"2\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"4.22\",\n    y1: \"10.22\",\n    x2: \"5.64\",\n    y2: \"11.64\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"1\",\n    y1: \"18\",\n    x2: \"3\",\n    y2: \"18\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"21\",\n    y1: \"18\",\n    x2: \"23\",\n    y2: \"18\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"18.36\",\n    y1: \"11.64\",\n    x2: \"19.78\",\n    y2: \"10.22\"\n  }), /*#__PURE__*/React.createElement(\"line\", {\n    x1: \"23\",\n    y1: \"22\",\n    x2: \"1\",\n    y2: \"22\"\n  }), /*#__PURE__*/React.createElement(\"polyline\", {\n    points: \"16 5 12 9 8 5\"\n  }));\n});\nSunset.propTypes = {\n  color: PropTypes.string,\n  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number])\n};\nSunset.displayName = 'Sunset';\nexport default Sunset;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}