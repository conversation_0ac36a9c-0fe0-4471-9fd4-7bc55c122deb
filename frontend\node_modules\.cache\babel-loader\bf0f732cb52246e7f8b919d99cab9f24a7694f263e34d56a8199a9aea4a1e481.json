{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { FiShoppingCart, FiTruck, FiShield, FiClock, FiStar } from 'react-icons/fi';\nimport { getFeaturedProducts } from '../store/slices/productSlice';\nimport { getCategories } from '../store/slices/categorySlice';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    featuredProducts,\n    isLoading\n  } = useSelector(state => state.products);\n  const {\n    categories\n  } = useSelector(state => state.categories);\n  useEffect(() => {\n    dispatch(getFeaturedProducts());\n    dispatch(getCategories());\n  }, [dispatch]);\n  const features = [{\n    icon: /*#__PURE__*/_jsxDEV(FiTruck, {\n      className: \"w-8 h-8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 13\n    }, this),\n    title: 'Free Delivery',\n    description: 'Free delivery on orders over $50'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FiShield, {\n      className: \"w-8 h-8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 13\n    }, this),\n    title: 'Quality Guarantee',\n    description: '100% fresh and quality products'\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(FiClock, {\n      className: \"w-8 h-8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this),\n    title: 'Fast Service',\n    description: 'Same day delivery available'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-gradient-to-r from-green-600 to-green-700 text-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-6xl font-bold leading-tight\",\n              children: [\"Bahuchar Groceries\", /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"block text-green-200\",\n                children: \"Fresh & Quality\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl text-green-100 max-w-lg\",\n              children: \"Your trusted neighborhood store for the freshest produce, pantry staples, and household essentials delivered with love and care.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products\",\n                className: \"bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center\",\n                children: \"Shop Now\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/products?category=fruits\",\n                className: \"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors text-center\",\n                children: \"Fresh Fruits\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden lg:block\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/api/placeholder/600/400\",\n              alt: \"Fresh Groceries\",\n              className: \"rounded-lg shadow-2xl\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n          children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center text-green-600\",\n              children: feature.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold text-gray-900\",\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Shop by Category\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 max-w-2xl mx-auto\",\n            children: \"Explore our wide range of fresh products organized by categories\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\",\n          children: categories.slice(0, 6).map(category => /*#__PURE__*/_jsxDEV(Link, {\n            to: `/products?category=${category._id}`,\n            className: \"group bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: category.icon || '🛒'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-semibold text-gray-900 group-hover:text-green-600 transition-colors\",\n              children: category.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-500 mt-1\",\n              children: [category.productCount || 0, \" items\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 17\n            }, this)]\n          }, category._id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-bold text-gray-900 mb-4\",\n            children: \"Featured Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 max-w-2xl mx-auto\",\n            children: \"Discover our handpicked selection of the finest products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center py-12\",\n          children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n            size: \"lg\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 13\n        }, this) : featuredProducts && featuredProducts.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\",\n          children: featuredProducts.slice(0, 8).map(product => {\n            var _product$images, _product$images$, _product$rating2;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow group\",\n              children: /*#__PURE__*/_jsxDEV(Link, {\n                to: `/products/${product._id}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : (_product$images$ = _product$images[0]) === null || _product$images$ === void 0 ? void 0 : _product$images$.url) || '/api/placeholder/300/300',\n                    alt: product.name,\n                    className: \"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"p-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors\",\n                    children: product.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 155,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center\",\n                      children: [...Array(5)].map((_, i) => {\n                        var _product$rating;\n                        return /*#__PURE__*/_jsxDEV(FiStar, {\n                          className: `w-4 h-4 ${i < Math.floor(((_product$rating = product.rating) === null || _product$rating === void 0 ? void 0 : _product$rating.average) || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                        }, i, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 161,\n                          columnNumber: 29\n                        }, this);\n                      })\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 159,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500 ml-2\",\n                      children: [\"(\", ((_product$rating2 = product.rating) === null || _product$rating2 === void 0 ? void 0 : _product$rating2.count) || 0, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 158,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-lg font-bold text-green-600\",\n                        children: formatPrice(product.discountedPrice || product.price)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 177,\n                        columnNumber: 27\n                      }, this), product.discountedPrice && /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-gray-500 line-through\",\n                        children: formatPrice(product.price)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors\",\n                      children: /*#__PURE__*/_jsxDEV(FiShoppingCart, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 187,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 175,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this)\n            }, product._id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-500 text-lg\",\n            children: \"No featured products available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-400 mt-2\",\n            children: [\"Products: \", (featuredProducts === null || featuredProducts === void 0 ? void 0 : featuredProducts.length) || 0, \", Loading: \", isLoading ? 'Yes' : 'No']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mt-12\",\n          children: /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/products\",\n            className: \"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\",\n            children: \"View All Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 bg-green-600\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-white mb-4\",\n          children: \"Stay Updated with Bahuchar Deals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-green-100 mb-8 max-w-2xl mx-auto\",\n          children: \"Subscribe to our newsletter and be the first to know about new products, special offers, and seasonal deals from Bahuchar Groceries Store.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md mx-auto flex\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            placeholder: \"Enter your email address\",\n            className: \"flex-1 px-4 py-3 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-300\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-green-800 text-white px-6 py-3 rounded-r-lg hover:bg-green-900 transition-colors\",\n            children: \"Subscribe\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Home, \"F6ZgZLapdyQFX0HG/+GX96PZoQs=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useEffect", "Link", "useDispatch", "useSelector", "FiShoppingCart", "FiTruck", "FiShield", "<PERSON><PERSON><PERSON>", "FiStar", "getFeaturedProducts", "getCategories", "formatPrice", "calculateDiscountPercentage", "LoadingSpinner", "jsxDEV", "_jsxDEV", "Home", "_s", "dispatch", "featuredProducts", "isLoading", "state", "products", "categories", "features", "icon", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "description", "children", "to", "src", "alt", "map", "feature", "index", "slice", "category", "_id", "name", "productCount", "size", "length", "product", "_product$images", "_product$images$", "_product$rating2", "images", "url", "Array", "_", "i", "_product$rating", "Math", "floor", "rating", "average", "count", "discountedPrice", "price", "type", "placeholder", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/Home.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { FiShoppingCart, FiTruck, FiShield, FiClock, FiStar } from 'react-icons/fi';\nimport { getFeaturedProducts } from '../store/slices/productSlice';\nimport { getCategories } from '../store/slices/categorySlice';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\n\nconst Home = () => {\n  const dispatch = useDispatch();\n  const { featuredProducts, isLoading } = useSelector((state) => state.products);\n  const { categories } = useSelector((state) => state.categories);\n\n  useEffect(() => {\n    dispatch(getFeaturedProducts());\n    dispatch(getCategories());\n  }, [dispatch]);\n\n  const features = [\n    {\n      icon: <FiTruck className=\"w-8 h-8\" />,\n      title: 'Free Delivery',\n      description: 'Free delivery on orders over $50'\n    },\n    {\n      icon: <FiShield className=\"w-8 h-8\" />,\n      title: 'Quality Guarantee',\n      description: '100% fresh and quality products'\n    },\n    {\n      icon: <FiClock className=\"w-8 h-8\" />,\n      title: 'Fast Service',\n      description: 'Same day delivery available'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"bg-gradient-to-r from-green-600 to-green-700 text-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n            <div className=\"space-y-6\">\n              <h1 className=\"text-4xl md:text-6xl font-bold leading-tight\">\n                Bahuchar Groceries\n                <span className=\"block text-green-200\">Fresh & Quality</span>\n              </h1>\n              <p className=\"text-xl text-green-100 max-w-lg\">\n                Your trusted neighborhood store for the freshest produce, pantry staples,\n                and household essentials delivered with love and care.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <Link \n                  to=\"/products\" \n                  className=\"bg-white text-green-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center\"\n                >\n                  Shop Now\n                </Link>\n                <Link \n                  to=\"/products?category=fruits\" \n                  className=\"border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-green-600 transition-colors text-center\"\n                >\n                  Fresh Fruits\n                </Link>\n              </div>\n            </div>\n            <div className=\"hidden lg:block\">\n              <img \n                src=\"/api/placeholder/600/400\" \n                alt=\"Fresh Groceries\" \n                className=\"rounded-lg shadow-2xl\"\n              />\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            {features.map((feature, index) => (\n              <div key={index} className=\"text-center space-y-4\">\n                <div className=\"flex justify-center text-green-600\">\n                  {feature.icon}\n                </div>\n                <h3 className=\"text-xl font-semibold text-gray-900\">{feature.title}</h3>\n                <p className=\"text-gray-600\">{feature.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Categories Section */}\n      <section className=\"py-16 bg-gray-50\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Shop by Category</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Explore our wide range of fresh products organized by categories\n            </p>\n          </div>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6\">\n            {categories.slice(0, 6).map((category) => (\n              <Link\n                key={category._id}\n                to={`/products?category=${category._id}`}\n                className=\"group bg-white rounded-lg p-6 text-center hover:shadow-lg transition-shadow\"\n              >\n                <div className=\"w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors\">\n                  <span className=\"text-2xl\">{category.icon || '🛒'}</span>\n                </div>\n                <h3 className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors\">\n                  {category.name}\n                </h3>\n                <p className=\"text-sm text-gray-500 mt-1\">\n                  {category.productCount || 0} items\n                </p>\n              </Link>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Products Section */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">Featured Products</h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Discover our handpicked selection of the finest products\n            </p>\n          </div>\n\n          {isLoading ? (\n            <div className=\"flex justify-center py-12\">\n              <LoadingSpinner size=\"lg\" />\n            </div>\n          ) : featuredProducts && featuredProducts.length > 0 ? (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {featuredProducts.slice(0, 8).map((product) => (\n                <div key={product._id} className=\"bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow group\">\n                  <Link to={`/products/${product._id}`}>\n                    <div className=\"aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg\">\n                      <img\n                        src={product.images?.[0]?.url || '/api/placeholder/300/300'}\n                        alt={product.name}\n                        className=\"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300\"\n                      />\n                    </div>\n                    <div className=\"p-4\">\n                      <h3 className=\"text-lg font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors\">\n                        {product.name}\n                      </h3>\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"flex items-center\">\n                          {[...Array(5)].map((_, i) => (\n                            <FiStar\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < Math.floor(product.rating?.average || 0)\n                                  ? 'text-yellow-400 fill-current'\n                                  : 'text-gray-300'\n                              }`}\n                            />\n                          ))}\n                        </div>\n                        <span className=\"text-sm text-gray-500 ml-2\">\n                          ({product.rating?.count || 0})\n                        </span>\n                      </div>\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-lg font-bold text-green-600\">\n                            {formatPrice(product.discountedPrice || product.price)}\n                          </span>\n                          {product.discountedPrice && (\n                            <span className=\"text-sm text-gray-500 line-through\">\n                              {formatPrice(product.price)}\n                            </span>\n                          )}\n                        </div>\n                        <button className=\"bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors\">\n                          <FiShoppingCart className=\"w-4 h-4\" />\n                        </button>\n                      </div>\n                    </div>\n                  </Link>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-12\">\n              <p className=\"text-gray-500 text-lg\">No featured products available</p>\n              <p className=\"text-sm text-gray-400 mt-2\">\n                Products: {featuredProducts?.length || 0}, Loading: {isLoading ? 'Yes' : 'No'}\n              </p>\n            </div>\n          )}\n\n          <div className=\"text-center mt-12\">\n            <Link \n              to=\"/products\" \n              className=\"bg-green-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors\"\n            >\n              View All Products\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* Newsletter Section */}\n      <section className=\"py-16 bg-green-600\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n          <h2 className=\"text-3xl font-bold text-white mb-4\">\n            Stay Updated with Bahuchar Deals\n          </h2>\n          <p className=\"text-green-100 mb-8 max-w-2xl mx-auto\">\n            Subscribe to our newsletter and be the first to know about new products,\n            special offers, and seasonal deals from Bahuchar Groceries Store.\n          </p>\n          <div className=\"max-w-md mx-auto flex\">\n            <input\n              type=\"email\"\n              placeholder=\"Enter your email address\"\n              className=\"flex-1 px-4 py-3 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-green-300\"\n            />\n            <button className=\"bg-green-800 text-white px-6 py-3 rounded-r-lg hover:bg-green-900 transition-colors\">\n              Subscribe\n            </button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n};\n\nexport default Home;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,cAAc,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,QAAQ,gBAAgB;AACnF,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,aAAa,QAAQ,+BAA+B;AAC7D,SAASC,WAAW,EAAEC,2BAA2B,QAAQ,mBAAmB;AAC5E,OAAOC,cAAc,MAAM,qCAAqC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEiB,gBAAgB;IAAEC;EAAU,CAAC,GAAGjB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACC,QAAQ,CAAC;EAC9E,MAAM;IAAEC;EAAW,CAAC,GAAGpB,WAAW,CAAEkB,KAAK,IAAKA,KAAK,CAACE,UAAU,CAAC;EAE/DvB,SAAS,CAAC,MAAM;IACdkB,QAAQ,CAACT,mBAAmB,CAAC,CAAC,CAAC;IAC/BS,QAAQ,CAACR,aAAa,CAAC,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACQ,QAAQ,CAAC,CAAC;EAEd,MAAMM,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEV,OAAA,CAACV,OAAO;MAACqB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrCC,KAAK,EAAE,eAAe;IACtBC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEV,OAAA,CAACT,QAAQ;MAACoB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtCC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACEP,IAAI,eAAEV,OAAA,CAACR,OAAO;MAACmB,SAAS,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrCC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEjB,OAAA;IAAKW,SAAS,EAAC,cAAc;IAAAO,QAAA,gBAE3BlB,OAAA;MAASW,SAAS,EAAC,yDAAyD;MAAAO,QAAA,eAC1ElB,OAAA;QAAKW,SAAS,EAAC,8CAA8C;QAAAO,QAAA,eAC3DlB,OAAA;UAAKW,SAAS,EAAC,qDAAqD;UAAAO,QAAA,gBAClElB,OAAA;YAAKW,SAAS,EAAC,WAAW;YAAAO,QAAA,gBACxBlB,OAAA;cAAIW,SAAS,EAAC,8CAA8C;cAAAO,QAAA,GAAC,oBAE3D,eAAAlB,OAAA;gBAAMW,SAAS,EAAC,sBAAsB;gBAAAO,QAAA,EAAC;cAAe;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC,eACLf,OAAA;cAAGW,SAAS,EAAC,iCAAiC;cAAAO,QAAA,EAAC;YAG/C;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJf,OAAA;cAAKW,SAAS,EAAC,iCAAiC;cAAAO,QAAA,gBAC9ClB,OAAA,CAACd,IAAI;gBACHiC,EAAE,EAAC,WAAW;gBACdR,SAAS,EAAC,4GAA4G;gBAAAO,QAAA,EACvH;cAED;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACPf,OAAA,CAACd,IAAI;gBACHiC,EAAE,EAAC,2BAA2B;gBAC9BR,SAAS,EAAC,uIAAuI;gBAAAO,QAAA,EAClJ;cAED;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNf,OAAA;YAAKW,SAAS,EAAC,iBAAiB;YAAAO,QAAA,eAC9BlB,OAAA;cACEoB,GAAG,EAAC,0BAA0B;cAC9BC,GAAG,EAAC,iBAAiB;cACrBV,SAAS,EAAC;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASW,SAAS,EAAC,gBAAgB;MAAAO,QAAA,eACjClB,OAAA;QAAKW,SAAS,EAAC,wCAAwC;QAAAO,QAAA,eACrDlB,OAAA;UAAKW,SAAS,EAAC,uCAAuC;UAAAO,QAAA,EACnDT,QAAQ,CAACa,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BxB,OAAA;YAAiBW,SAAS,EAAC,uBAAuB;YAAAO,QAAA,gBAChDlB,OAAA;cAAKW,SAAS,EAAC,oCAAoC;cAAAO,QAAA,EAChDK,OAAO,CAACb;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACNf,OAAA;cAAIW,SAAS,EAAC,qCAAqC;cAAAO,QAAA,EAAEK,OAAO,CAACP;YAAK;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxEf,OAAA;cAAGW,SAAS,EAAC,eAAe;cAAAO,QAAA,EAAEK,OAAO,CAACN;YAAW;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA,GAL9CS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASW,SAAS,EAAC,kBAAkB;MAAAO,QAAA,eACnClB,OAAA;QAAKW,SAAS,EAAC,wCAAwC;QAAAO,QAAA,gBACrDlB,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAO,QAAA,gBAChClB,OAAA;YAAIW,SAAS,EAAC,uCAAuC;YAAAO,QAAA,EAAC;UAAgB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3Ef,OAAA;YAAGW,SAAS,EAAC,iCAAiC;YAAAO,QAAA,EAAC;UAE/C;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENf,OAAA;UAAKW,SAAS,EAAC,sDAAsD;UAAAO,QAAA,EAClEV,UAAU,CAACiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEI,QAAQ,iBACnC1B,OAAA,CAACd,IAAI;YAEHiC,EAAE,EAAE,sBAAsBO,QAAQ,CAACC,GAAG,EAAG;YACzChB,SAAS,EAAC,6EAA6E;YAAAO,QAAA,gBAEvFlB,OAAA;cAAKW,SAAS,EAAC,8HAA8H;cAAAO,QAAA,eAC3IlB,OAAA;gBAAMW,SAAS,EAAC,UAAU;gBAAAO,QAAA,EAAEQ,QAAQ,CAAChB,IAAI,IAAI;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC,eACNf,OAAA;cAAIW,SAAS,EAAC,0EAA0E;cAAAO,QAAA,EACrFQ,QAAQ,CAACE;YAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLf,OAAA;cAAGW,SAAS,EAAC,4BAA4B;cAAAO,QAAA,GACtCQ,QAAQ,CAACG,YAAY,IAAI,CAAC,EAAC,QAC9B;YAAA;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA,GAZCW,QAAQ,CAACC,GAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAab,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASW,SAAS,EAAC,gBAAgB;MAAAO,QAAA,eACjClB,OAAA;QAAKW,SAAS,EAAC,wCAAwC;QAAAO,QAAA,gBACrDlB,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAO,QAAA,gBAChClB,OAAA;YAAIW,SAAS,EAAC,uCAAuC;YAAAO,QAAA,EAAC;UAAiB;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5Ef,OAAA;YAAGW,SAAS,EAAC,iCAAiC;YAAAO,QAAA,EAAC;UAE/C;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,EAELV,SAAS,gBACRL,OAAA;UAAKW,SAAS,EAAC,2BAA2B;UAAAO,QAAA,eACxClB,OAAA,CAACF,cAAc;YAACgC,IAAI,EAAC;UAAI;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,GACJX,gBAAgB,IAAIA,gBAAgB,CAAC2B,MAAM,GAAG,CAAC,gBACjD/B,OAAA;UAAKW,SAAS,EAAC,sDAAsD;UAAAO,QAAA,EAClEd,gBAAgB,CAACqB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAEU,OAAO;YAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;YAAA,oBACxCnC,OAAA;cAAuBW,SAAS,EAAC,uEAAuE;cAAAO,QAAA,eACtGlB,OAAA,CAACd,IAAI;gBAACiC,EAAE,EAAE,aAAaa,OAAO,CAACL,GAAG,EAAG;gBAAAT,QAAA,gBACnClB,OAAA;kBAAKW,SAAS,EAAC,2DAA2D;kBAAAO,QAAA,eACxElB,OAAA;oBACEoB,GAAG,EAAE,EAAAa,eAAA,GAAAD,OAAO,CAACI,MAAM,cAAAH,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,uBAAnBA,gBAAA,CAAqBG,GAAG,KAAI,0BAA2B;oBAC5DhB,GAAG,EAAEW,OAAO,CAACJ,IAAK;oBAClBjB,SAAS,EAAC;kBAAkF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7F;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eACNf,OAAA;kBAAKW,SAAS,EAAC,KAAK;kBAAAO,QAAA,gBAClBlB,OAAA;oBAAIW,SAAS,EAAC,uFAAuF;oBAAAO,QAAA,EAClGc,OAAO,CAACJ;kBAAI;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC,eACLf,OAAA;oBAAKW,SAAS,EAAC,wBAAwB;oBAAAO,QAAA,gBACrClB,OAAA;sBAAKW,SAAS,EAAC,mBAAmB;sBAAAO,QAAA,EAC/B,CAAC,GAAGoB,KAAK,CAAC,CAAC,CAAC,CAAC,CAAChB,GAAG,CAAC,CAACiB,CAAC,EAAEC,CAAC;wBAAA,IAAAC,eAAA;wBAAA,oBACtBzC,OAAA,CAACP,MAAM;0BAELkB,SAAS,EAAE,WACT6B,CAAC,GAAGE,IAAI,CAACC,KAAK,CAAC,EAAAF,eAAA,GAAAT,OAAO,CAACY,MAAM,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,OAAO,KAAI,CAAC,CAAC,GACxC,8BAA8B,GAC9B,eAAe;wBAClB,GALEL,CAAC;0BAAA5B,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAMP,CAAC;sBAAA,CACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACNf,OAAA;sBAAMW,SAAS,EAAC,4BAA4B;sBAAAO,QAAA,GAAC,GAC1C,EAAC,EAAAiB,gBAAA,GAAAH,OAAO,CAACY,MAAM,cAAAT,gBAAA,uBAAdA,gBAAA,CAAgBW,KAAK,KAAI,CAAC,EAAC,GAC/B;oBAAA;sBAAAlC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNf,OAAA;oBAAKW,SAAS,EAAC,mCAAmC;oBAAAO,QAAA,gBAChDlB,OAAA;sBAAKW,SAAS,EAAC,6BAA6B;sBAAAO,QAAA,gBAC1ClB,OAAA;wBAAMW,SAAS,EAAC,kCAAkC;wBAAAO,QAAA,EAC/CtB,WAAW,CAACoC,OAAO,CAACe,eAAe,IAAIf,OAAO,CAACgB,KAAK;sBAAC;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC,EACNiB,OAAO,CAACe,eAAe,iBACtB/C,OAAA;wBAAMW,SAAS,EAAC,oCAAoC;wBAAAO,QAAA,EACjDtB,WAAW,CAACoC,OAAO,CAACgB,KAAK;sBAAC;wBAAApC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACNf,OAAA;sBAAQW,SAAS,EAAC,6EAA6E;sBAAAO,QAAA,eAC7FlB,OAAA,CAACX,cAAc;wBAACsB,SAAS,EAAC;sBAAS;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GA9CCiB,OAAO,CAACL,GAAG;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+ChB,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,gBAENf,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAO,QAAA,gBAChClB,OAAA;YAAGW,SAAS,EAAC,uBAAuB;YAAAO,QAAA,EAAC;UAA8B;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACvEf,OAAA;YAAGW,SAAS,EAAC,4BAA4B;YAAAO,QAAA,GAAC,YAC9B,EAAC,CAAAd,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE2B,MAAM,KAAI,CAAC,EAAC,aAAW,EAAC1B,SAAS,GAAG,KAAK,GAAG,IAAI;UAAA;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACN,eAEDf,OAAA;UAAKW,SAAS,EAAC,mBAAmB;UAAAO,QAAA,eAChClB,OAAA,CAACd,IAAI;YACHiC,EAAE,EAAC,WAAW;YACdR,SAAS,EAAC,iGAAiG;YAAAO,QAAA,EAC5G;UAED;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVf,OAAA;MAASW,SAAS,EAAC,oBAAoB;MAAAO,QAAA,eACrClB,OAAA;QAAKW,SAAS,EAAC,oDAAoD;QAAAO,QAAA,gBACjElB,OAAA;UAAIW,SAAS,EAAC,oCAAoC;UAAAO,QAAA,EAAC;QAEnD;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLf,OAAA;UAAGW,SAAS,EAAC,uCAAuC;UAAAO,QAAA,EAAC;QAGrD;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJf,OAAA;UAAKW,SAAS,EAAC,uBAAuB;UAAAO,QAAA,gBACpClB,OAAA;YACEiD,IAAI,EAAC,OAAO;YACZC,WAAW,EAAC,0BAA0B;YACtCvC,SAAS,EAAC;UAAoF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eACFf,OAAA;YAAQW,SAAS,EAAC,qFAAqF;YAAAO,QAAA,EAAC;UAExG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAACb,EAAA,CArOID,IAAI;EAAA,QACSd,WAAW,EACYC,WAAW,EAC5BA,WAAW;AAAA;AAAA+D,EAAA,GAH9BlD,IAAI;AAuOV,eAAeA,IAAI;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}