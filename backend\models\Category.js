const mongoose = require('mongoose');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    unique: true,
    maxlength: [50, 'Category name cannot exceed 50 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    trim: true
  },
  image: {
    url: String,
    alt: String
  },
  icon: {
    type: String,
    trim: true
  },
  parent: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  subcategories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],
  level: {
    type: Number,
    default: 0,
    min: 0,
    max: 3
  },
  sortOrder: {
    type: Number,
    default: 0
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  productCount: {
    type: Number,
    default: 0
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String]
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes
categorySchema.index({ parent: 1 });
categorySchema.index({ isActive: 1 });
categorySchema.index({ sortOrder: 1 });

// Pre-save middleware to generate slug
categorySchema.pre('save', function(next) {
  if (this.isModified('name') || this.isNew) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Pre-save middleware to set level based on parent
categorySchema.pre('save', async function(next) {
  if (this.parent) {
    try {
      const parentCategory = await this.constructor.findById(this.parent);
      if (parentCategory) {
        this.level = parentCategory.level + 1;
      }
    } catch (error) {
      return next(error);
    }
  } else {
    this.level = 0;
  }
  next();
});

// Post-save middleware to update parent's subcategories
categorySchema.post('save', async function(doc) {
  if (doc.parent) {
    try {
      await this.constructor.findByIdAndUpdate(
        doc.parent,
        { $addToSet: { subcategories: doc._id } }
      );
    } catch (error) {
      console.error('Error updating parent subcategories:', error);
    }
  }
});

// Pre-remove middleware to handle subcategories
categorySchema.pre('remove', async function(next) {
  try {
    // Remove this category from parent's subcategories
    if (this.parent) {
      await this.constructor.findByIdAndUpdate(
        this.parent,
        { $pull: { subcategories: this._id } }
      );
    }

    // Handle subcategories - either delete them or move them to parent
    if (this.subcategories && this.subcategories.length > 0) {
      // Option 1: Delete all subcategories
      await this.constructor.deleteMany({ _id: { $in: this.subcategories } });
      
      // Option 2: Move subcategories to this category's parent
      // await this.constructor.updateMany(
      //   { _id: { $in: this.subcategories } },
      //   { parent: this.parent }
      // );
    }

    next();
  } catch (error) {
    next(error);
  }
});

// Virtual for full path (breadcrumb)
categorySchema.virtual('fullPath').get(function() {
  // This would need to be populated with parent data
  return this.name;
});

// Static method to get category tree
categorySchema.statics.getCategoryTree = async function() {
  try {
    const categories = await this.find({ isActive: true })
      .sort({ level: 1, sortOrder: 1, name: 1 })
      .populate('subcategories', 'name slug level isActive');
    
    const buildTree = (categories, parentId = null) => {
      return categories
        .filter(cat => String(cat.parent) === String(parentId))
        .map(cat => ({
          ...cat.toObject(),
          children: buildTree(categories, cat._id)
        }));
    };

    return buildTree(categories);
  } catch (error) {
    throw error;
  }
};

// Static method to get featured categories
categorySchema.statics.getFeatured = function() {
  return this.find({ isFeatured: true, isActive: true })
    .sort({ sortOrder: 1, name: 1 });
};

// Static method to get root categories
categorySchema.statics.getRootCategories = function() {
  return this.find({ parent: null, isActive: true })
    .sort({ sortOrder: 1, name: 1 });
};

// Instance method to get all ancestors
categorySchema.methods.getAncestors = async function() {
  const ancestors = [];
  let current = this;
  
  while (current.parent) {
    const parent = await this.constructor.findById(current.parent);
    if (parent) {
      ancestors.unshift(parent);
      current = parent;
    } else {
      break;
    }
  }
  
  return ancestors;
};

// Instance method to get all descendants
categorySchema.methods.getDescendants = async function() {
  const descendants = [];
  
  const getChildren = async (categoryId) => {
    const children = await this.constructor.find({ parent: categoryId });
    for (const child of children) {
      descendants.push(child);
      await getChildren(child._id);
    }
  };
  
  await getChildren(this._id);
  return descendants;
};

module.exports = mongoose.model('Category', categorySchema);
