{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\Products.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useSearchParams, Link, useNavigate } from 'react-router-dom';\nimport { FiGrid, FiList, FiFilter, FiStar, FiShoppingCart } from 'react-icons/fi';\nimport { getProducts, setFilters } from '../store/slices/productSlice';\nimport { addToCart, getCart } from '../store/slices/cartSlice';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Products = () => {\n  _s();\n  var _categories$find;\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showFilters, setShowFilters] = useState(false);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const {\n    products,\n    isLoading,\n    totalPages,\n    currentPage,\n    filters\n  } = useSelector(state => state.products);\n  const {\n    categories\n  } = useSelector(state => state.categories);\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  useEffect(() => {\n    const params = {\n      page: searchParams.get('page') || 1,\n      category: searchParams.get('category') || '',\n      search: searchParams.get('search') || '',\n      minPrice: searchParams.get('minPrice') || '',\n      maxPrice: searchParams.get('maxPrice') || '',\n      rating: searchParams.get('rating') || '',\n      sortBy: searchParams.get('sortBy') || 'createdAt',\n      sortOrder: searchParams.get('sortOrder') || 'desc'\n    };\n    dispatch(setFilters(params));\n    dispatch(getProducts(params));\n  }, [searchParams, dispatch]);\n  const handleFilterChange = (key, value) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (value) {\n      newParams.set(key, value);\n    } else {\n      newParams.delete(key);\n    }\n    newParams.set('page', '1'); // Reset to first page\n    setSearchParams(newParams);\n  };\n  const handlePageChange = page => {\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', page);\n    setSearchParams(newParams);\n  };\n  const clearFilters = () => {\n    setSearchParams({});\n  };\n  const handleAddToCart = async (product, e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (!user || !token) {\n      toast.error('Please login to add items to cart');\n      navigate('/login');\n      return;\n    }\n    try {\n      await dispatch(addToCart({\n        productId: product._id,\n        quantity: 1\n      })).unwrap();\n      toast.success(`${product.name} added to cart!`);\n\n      // Refresh cart to ensure UI is updated\n      dispatch(getCart());\n    } catch (error) {\n      toast.error('Failed to add item to cart');\n      console.error('Add to cart error:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-900 mb-2\",\n            children: \"Products\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: [searchParams.get('search') && `Search results for \"${searchParams.get('search')}\"`, searchParams.get('category') && `Category: ${(_categories$find = categories.find(c => c._id === searchParams.get('category'))) === null || _categories$find === void 0 ? void 0 : _categories$find.name}`]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 mt-4 md:mt-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex bg-white rounded-lg border\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('grid'),\n              className: `p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-l-lg`,\n              children: /*#__PURE__*/_jsxDEV(FiGrid, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setViewMode('list'),\n              className: `p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-r-lg`,\n              children: /*#__PURE__*/_jsxDEV(FiList, {\n                className: \"w-5 h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowFilters(!showFilters),\n            className: \"flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(FiFilter, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n            value: `${filters.sortBy}-${filters.sortOrder}`,\n            onChange: e => {\n              const [sortBy, sortOrder] = e.target.value.split('-');\n              handleFilterChange('sortBy', sortBy);\n              handleFilterChange('sortOrder', sortOrder);\n            },\n            className: \"bg-white border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"createdAt-desc\",\n              children: \"Newest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"createdAt-asc\",\n              children: \"Oldest First\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"price-asc\",\n              children: \"Price: Low to High\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"price-desc\",\n              children: \"Price: High to Low\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name-asc\",\n              children: \"Name: A to Z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"name-desc\",\n              children: \"Name: Z to A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"rating.average-desc\",\n              children: \"Highest Rated\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col lg:flex-row gap-8\",\n        children: [showFilters && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:w-64 bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between items-center mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-lg font-semibold\",\n              children: \"Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"text-sm text-green-600 hover:text-green-700\",\n              children: \"Clear All\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Categories\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: categories.map(category => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"category\",\n                  value: category._id,\n                  checked: filters.category === category._id,\n                  onChange: e => handleFilterChange('category', e.target.value),\n                  className: \"text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ml-2 text-sm\",\n                  children: category.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 23\n                }, this)]\n              }, category._id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Price Range\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"Min Price\",\n                value: filters.minPrice,\n                onChange: e => handleFilterChange('minPrice', e.target.value),\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                placeholder: \"Max Price\",\n                value: filters.maxPrice,\n                onChange: e => handleFilterChange('maxPrice', e.target.value),\n                className: \"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium mb-3\",\n              children: \"Rating\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [4, 3, 2, 1].map(rating => /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  name: \"rating\",\n                  value: rating,\n                  checked: filters.rating === rating.toString(),\n                  onChange: e => handleFilterChange('rating', e.target.value),\n                  className: \"text-green-600 focus:ring-green-500\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-2 flex items-center\",\n                  children: [[...Array(5)].map((_, i) => /*#__PURE__*/_jsxDEV(FiStar, {\n                    className: `w-4 h-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                  }, i, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 27\n                  }, this)), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1 text-sm\",\n                    children: \"& up\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this)]\n              }, rating, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1\",\n          children: isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center py-12\",\n            children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n              size: \"lg\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 15\n          }, this) : products.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-12\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-500 text-lg\",\n              children: \"No products found\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: clearFilters,\n              className: \"mt-4 text-green-600 hover:text-green-700\",\n              children: \"Clear filters to see all products\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'grid-cols-1'}`,\n              children: products.map(product => {\n                var _product$images, _product$images$, _product$stock, _product$stock2, _product$stock3, _product$category, _product$rating2, _product$stock4, _product$stock5, _product$stock6, _product$stock7;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow ${viewMode === 'list' ? 'flex' : ''}`,\n                  children: /*#__PURE__*/_jsxDEV(Link, {\n                    to: `/products/${product._id}`,\n                    className: viewMode === 'list' ? 'flex w-full' : '',\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`,\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : (_product$images$ = _product$images[0]) === null || _product$images$ === void 0 ? void 0 : _product$images$.url) || '/api/placeholder/300/300',\n                        alt: product.name,\n                        className: `w-full object-cover ${viewMode === 'list' ? 'h-32 rounded-l-lg' : 'h-48 rounded-t-lg'}`\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 264,\n                        columnNumber: 27\n                      }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-bold\",\n                        children: [calculateDiscountPercentage(product.originalPrice, product.price), \"% OFF\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 29\n                      }, this), (((_product$stock = product.stock) === null || _product$stock === void 0 ? void 0 : _product$stock.quantity) || 0) === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-t-lg\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-white font-bold text-sm\",\n                          children: \"OUT OF STOCK\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 280,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 279,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"p-4 flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-start justify-between mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\",\n                          children: product.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 286,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: `px-2 py-1 rounded-full text-xs font-semibold ${(((_product$stock2 = product.stock) === null || _product$stock2 === void 0 ? void 0 : _product$stock2.quantity) || 0) > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                          children: (((_product$stock3 = product.stock) === null || _product$stock3 === void 0 ? void 0 : _product$stock3.quantity) || 0) > 0 ? 'In Stock' : 'Out of Stock'\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 290,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 285,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [product.brand && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\",\n                          children: product.brand\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 302,\n                          columnNumber: 31\n                        }, this), ((_product$category = product.category) === null || _product$category === void 0 ? void 0 : _product$category.name) && /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n                          children: product.category.name\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 307,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 300,\n                        columnNumber: 27\n                      }, this), viewMode === 'list' && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-gray-600 text-sm mb-2 line-clamp-2\",\n                        children: product.description\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 314,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center\",\n                          children: [...Array(5)].map((_, i) => {\n                            var _product$rating;\n                            return /*#__PURE__*/_jsxDEV(FiStar, {\n                              className: `w-4 h-4 ${i < Math.floor(((_product$rating = product.rating) === null || _product$rating === void 0 ? void 0 : _product$rating.average) || 0) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`\n                            }, i, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 321,\n                              columnNumber: 33\n                            }, this);\n                          })\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 319,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-sm text-gray-500 ml-2\",\n                          children: [\"(\", ((_product$rating2 = product.rating) === null || _product$rating2 === void 0 ? void 0 : _product$rating2.count) || 0, \")\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 331,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-col\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                              className: \"text-lg font-bold text-green-600\",\n                              children: formatPrice(product.price)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 338,\n                              columnNumber: 33\n                            }, this), product.originalPrice && product.originalPrice > product.price && /*#__PURE__*/_jsxDEV(_Fragment, {\n                              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-sm text-gray-500 line-through\",\n                                children: formatPrice(product.originalPrice)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 343,\n                                columnNumber: 37\n                              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                                className: \"text-xs bg-red-500 text-white px-2 py-1 rounded-full font-semibold\",\n                                children: [calculateDiscountPercentage(product.originalPrice, product.price), \"% OFF\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 346,\n                                columnNumber: 37\n                              }, this)]\n                            }, void 0, true)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 337,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: [((_product$stock4 = product.stock) === null || _product$stock4 === void 0 ? void 0 : _product$stock4.quantity) || 0, \" units available\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 353,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 336,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: e => handleAddToCart(product, e),\n                          disabled: (((_product$stock5 = product.stock) === null || _product$stock5 === void 0 ? void 0 : _product$stock5.quantity) || 0) === 0,\n                          className: `p-2 rounded-lg transition-colors ${(((_product$stock6 = product.stock) === null || _product$stock6 === void 0 ? void 0 : _product$stock6.quantity) || 0) === 0 ? 'bg-gray-400 text-gray-600 cursor-not-allowed' : 'bg-green-600 text-white hover:bg-green-700'}`,\n                          title: (((_product$stock7 = product.stock) === null || _product$stock7 === void 0 ? void 0 : _product$stock7.quantity) || 0) === 0 ? 'Out of Stock' : 'Add to Cart',\n                          children: /*#__PURE__*/_jsxDEV(FiShoppingCart, {\n                            className: \"w-4 h-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 367,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 357,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 335,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 284,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 23\n                  }, this)\n                }, product._id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), totalPages > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-center mt-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-2\",\n                children: [...Array(totalPages)].map((_, i) => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handlePageChange(i + 1),\n                  className: `px-3 py-2 rounded-lg ${currentPage === i + 1 ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-100'}`,\n                  children: i + 1\n                }, i + 1, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 381,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(Products, \"hOUeaeKwF9GitgZ/AqOKqHUny1g=\", false, function () {\n  return [useSearchParams, useDispatch, useNavigate, useSelector, useSelector, useSelector];\n});\n_c = Products;\nexport default Products;\nvar _c;\n$RefreshReg$(_c, \"Products\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "useSelector", "useSearchParams", "Link", "useNavigate", "<PERSON><PERSON><PERSON>", "FiList", "<PERSON><PERSON><PERSON><PERSON>", "FiStar", "FiShoppingCart", "getProducts", "setFilters", "addToCart", "getCart", "formatPrice", "calculateDiscountPercentage", "LoadingSpinner", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Products", "_s", "_categories$find", "searchParams", "setSearchParams", "viewMode", "setViewMode", "showFilters", "setShowFilters", "dispatch", "navigate", "products", "isLoading", "totalPages", "currentPage", "filters", "state", "categories", "user", "token", "auth", "params", "page", "get", "category", "search", "minPrice", "maxPrice", "rating", "sortBy", "sortOrder", "handleFilterChange", "key", "value", "newParams", "URLSearchParams", "set", "delete", "handlePageChange", "clearFilters", "handleAddToCart", "product", "e", "preventDefault", "stopPropagation", "error", "productId", "_id", "quantity", "unwrap", "success", "name", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "find", "c", "onClick", "onChange", "target", "split", "map", "type", "checked", "placeholder", "toString", "Array", "_", "i", "size", "length", "_product$images", "_product$images$", "_product$stock", "_product$stock2", "_product$stock3", "_product$category", "_product$rating2", "_product$stock4", "_product$stock5", "_product$stock6", "_product$stock7", "to", "src", "images", "url", "alt", "originalPrice", "price", "stock", "brand", "description", "_product$rating", "Math", "floor", "average", "count", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/Products.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useSearchParams, Link, useNavigate } from 'react-router-dom';\nimport { FiGrid, FiList, FiFilter, FiStar, FiShoppingCart } from 'react-icons/fi';\nimport { getProducts, setFilters } from '../store/slices/productSlice';\nimport { addToCart, getCart } from '../store/slices/cartSlice';\nimport { formatPrice, calculateDiscountPercentage } from '../utils/currency';\nimport LoadingSpinner from '../components/common/LoadingSpinner';\nimport toast from 'react-hot-toast';\n\nconst Products = () => {\n  const [searchParams, setSearchParams] = useSearchParams();\n  const [viewMode, setViewMode] = useState('grid');\n  const [showFilters, setShowFilters] = useState(false);\n\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const { products, isLoading, totalPages, currentPage, filters } = useSelector((state) => state.products);\n  const { categories } = useSelector((state) => state.categories);\n  const { user, token } = useSelector((state) => state.auth);\n\n  useEffect(() => {\n    const params = {\n      page: searchParams.get('page') || 1,\n      category: searchParams.get('category') || '',\n      search: searchParams.get('search') || '',\n      minPrice: searchParams.get('minPrice') || '',\n      maxPrice: searchParams.get('maxPrice') || '',\n      rating: searchParams.get('rating') || '',\n      sortBy: searchParams.get('sortBy') || 'createdAt',\n      sortOrder: searchParams.get('sortOrder') || 'desc',\n    };\n\n    dispatch(setFilters(params));\n    dispatch(getProducts(params));\n  }, [searchParams, dispatch]);\n\n  const handleFilterChange = (key, value) => {\n    const newParams = new URLSearchParams(searchParams);\n    if (value) {\n      newParams.set(key, value);\n    } else {\n      newParams.delete(key);\n    }\n    newParams.set('page', '1'); // Reset to first page\n    setSearchParams(newParams);\n  };\n\n  const handlePageChange = (page) => {\n    const newParams = new URLSearchParams(searchParams);\n    newParams.set('page', page);\n    setSearchParams(newParams);\n  };\n\n  const clearFilters = () => {\n    setSearchParams({});\n  };\n\n  const handleAddToCart = async (product, e) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (!user || !token) {\n      toast.error('Please login to add items to cart');\n      navigate('/login');\n      return;\n    }\n\n    try {\n      await dispatch(addToCart({\n        productId: product._id,\n        quantity: 1\n      })).unwrap();\n\n      toast.success(`${product.name} added to cart!`);\n\n      // Refresh cart to ensure UI is updated\n      dispatch(getCart());\n    } catch (error) {\n      toast.error('Failed to add item to cart');\n      console.error('Add to cart error:', error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Products</h1>\n            <p className=\"text-gray-600\">\n              {searchParams.get('search') && `Search results for \"${searchParams.get('search')}\"`}\n              {searchParams.get('category') && `Category: ${categories.find(c => c._id === searchParams.get('category'))?.name}`}\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-4 mt-4 md:mt-0\">\n            {/* View Mode Toggle */}\n            <div className=\"flex bg-white rounded-lg border\">\n              <button\n                onClick={() => setViewMode('grid')}\n                className={`p-2 ${viewMode === 'grid' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-l-lg`}\n              >\n                <FiGrid className=\"w-5 h-5\" />\n              </button>\n              <button\n                onClick={() => setViewMode('list')}\n                className={`p-2 ${viewMode === 'list' ? 'bg-green-600 text-white' : 'text-gray-600'} rounded-r-lg`}\n              >\n                <FiList className=\"w-5 h-5\" />\n              </button>\n            </div>\n\n            {/* Filter Toggle */}\n            <button\n              onClick={() => setShowFilters(!showFilters)}\n              className=\"flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border hover:bg-gray-50\"\n            >\n              <FiFilter className=\"w-5 h-5\" />\n              <span>Filters</span>\n            </button>\n\n            {/* Sort */}\n            <select\n              value={`${filters.sortBy}-${filters.sortOrder}`}\n              onChange={(e) => {\n                const [sortBy, sortOrder] = e.target.value.split('-');\n                handleFilterChange('sortBy', sortBy);\n                handleFilterChange('sortOrder', sortOrder);\n              }}\n              className=\"bg-white border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500\"\n            >\n              <option value=\"createdAt-desc\">Newest First</option>\n              <option value=\"createdAt-asc\">Oldest First</option>\n              <option value=\"price-asc\">Price: Low to High</option>\n              <option value=\"price-desc\">Price: High to Low</option>\n              <option value=\"name-asc\">Name: A to Z</option>\n              <option value=\"name-desc\">Name: Z to A</option>\n              <option value=\"rating.average-desc\">Highest Rated</option>\n            </select>\n          </div>\n        </div>\n\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Filters Sidebar */}\n          {showFilters && (\n            <div className=\"lg:w-64 bg-white rounded-lg shadow-md p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h3 className=\"text-lg font-semibold\">Filters</h3>\n                <button\n                  onClick={clearFilters}\n                  className=\"text-sm text-green-600 hover:text-green-700\"\n                >\n                  Clear All\n                </button>\n              </div>\n\n              {/* Categories */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Categories</h4>\n                <div className=\"space-y-2\">\n                  {categories.map((category) => (\n                    <label key={category._id} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"category\"\n                        value={category._id}\n                        checked={filters.category === category._id}\n                        onChange={(e) => handleFilterChange('category', e.target.value)}\n                        className=\"text-green-600 focus:ring-green-500\"\n                      />\n                      <span className=\"ml-2 text-sm\">{category.name}</span>\n                    </label>\n                  ))}\n                </div>\n              </div>\n\n              {/* Price Range */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Price Range</h4>\n                <div className=\"space-y-2\">\n                  <input\n                    type=\"number\"\n                    placeholder=\"Min Price\"\n                    value={filters.minPrice}\n                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  />\n                  <input\n                    type=\"number\"\n                    placeholder=\"Max Price\"\n                    value={filters.maxPrice}\n                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}\n                    className=\"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  />\n                </div>\n              </div>\n\n              {/* Rating */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-medium mb-3\">Rating</h4>\n                <div className=\"space-y-2\">\n                  {[4, 3, 2, 1].map((rating) => (\n                    <label key={rating} className=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        name=\"rating\"\n                        value={rating}\n                        checked={filters.rating === rating.toString()}\n                        onChange={(e) => handleFilterChange('rating', e.target.value)}\n                        className=\"text-green-600 focus:ring-green-500\"\n                      />\n                      <div className=\"ml-2 flex items-center\">\n                        {[...Array(5)].map((_, i) => (\n                          <FiStar\n                            key={i}\n                            className={`w-4 h-4 ${\n                              i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'\n                            }`}\n                          />\n                        ))}\n                        <span className=\"ml-1 text-sm\">& up</span>\n                      </div>\n                    </label>\n                  ))}\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Products Grid/List */}\n          <div className=\"flex-1\">\n            {isLoading ? (\n              <div className=\"flex justify-center py-12\">\n                <LoadingSpinner size=\"lg\" />\n              </div>\n            ) : products.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <p className=\"text-gray-500 text-lg\">No products found</p>\n                <button\n                  onClick={clearFilters}\n                  className=\"mt-4 text-green-600 hover:text-green-700\"\n                >\n                  Clear filters to see all products\n                </button>\n              </div>\n            ) : (\n              <>\n                <div className={`grid gap-6 ${\n                  viewMode === 'grid' \n                    ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' \n                    : 'grid-cols-1'\n                }`}>\n                  {products.map((product) => (\n                    <div\n                      key={product._id}\n                      className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow ${\n                        viewMode === 'list' ? 'flex' : ''\n                      }`}\n                    >\n                      <Link to={`/products/${product._id}`} className={viewMode === 'list' ? 'flex w-full' : ''}>\n                        <div className={`relative ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>\n                          <img\n                            src={product.images?.[0]?.url || '/api/placeholder/300/300'}\n                            alt={product.name}\n                            className={`w-full object-cover ${\n                              viewMode === 'list' ? 'h-32 rounded-l-lg' : 'h-48 rounded-t-lg'\n                            }`}\n                          />\n                          {/* Discount Badge */}\n                          {product.originalPrice && product.originalPrice > product.price && (\n                            <div className=\"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded-md text-xs font-bold\">\n                              {calculateDiscountPercentage(product.originalPrice, product.price)}% OFF\n                            </div>\n                          )}\n                          {/* Out of Stock Overlay */}\n                          {(product.stock?.quantity || 0) === 0 && (\n                            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded-t-lg\">\n                              <span className=\"text-white font-bold text-sm\">OUT OF STOCK</span>\n                            </div>\n                          )}\n                        </div>\n                        <div className=\"p-4 flex-1\">\n                          <div className=\"flex items-start justify-between mb-2\">\n                            <h3 className=\"text-lg font-semibold text-gray-900 hover:text-green-600 transition-colors\">\n                              {product.name}\n                            </h3>\n                            {/* Stock Status */}\n                            <span className={`px-2 py-1 rounded-full text-xs font-semibold ${\n                              (product.stock?.quantity || 0) > 0\n                                ? 'bg-green-100 text-green-800'\n                                : 'bg-red-100 text-red-800'\n                            }`}>\n                              {(product.stock?.quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'}\n                            </span>\n                          </div>\n\n                          {/* Brand and Category */}\n                          <div className=\"flex items-center space-x-2 mb-2\">\n                            {product.brand && (\n                              <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded\">\n                                {product.brand}\n                              </span>\n                            )}\n                            {product.category?.name && (\n                              <span className=\"text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded\">\n                                {product.category.name}\n                              </span>\n                            )}\n                          </div>\n\n                          {viewMode === 'list' && (\n                            <p className=\"text-gray-600 text-sm mb-2 line-clamp-2\">\n                              {product.description}\n                            </p>\n                          )}\n                          <div className=\"flex items-center mb-2\">\n                            <div className=\"flex items-center\">\n                              {[...Array(5)].map((_, i) => (\n                                <FiStar\n                                  key={i}\n                                  className={`w-4 h-4 ${\n                                    i < Math.floor(product.rating?.average || 0)\n                                      ? 'text-yellow-400 fill-current'\n                                      : 'text-gray-300'\n                                  }`}\n                                />\n                              ))}\n                            </div>\n                            <span className=\"text-sm text-gray-500 ml-2\">\n                              ({product.rating?.count || 0})\n                            </span>\n                          </div>\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"flex flex-col\">\n                              <div className=\"flex items-center space-x-2\">\n                                <span className=\"text-lg font-bold text-green-600\">\n                                  {formatPrice(product.price)}\n                                </span>\n                                {product.originalPrice && product.originalPrice > product.price && (\n                                  <>\n                                    <span className=\"text-sm text-gray-500 line-through\">\n                                      {formatPrice(product.originalPrice)}\n                                    </span>\n                                    <span className=\"text-xs bg-red-500 text-white px-2 py-1 rounded-full font-semibold\">\n                                      {calculateDiscountPercentage(product.originalPrice, product.price)}% OFF\n                                    </span>\n                                  </>\n                                )}\n                              </div>\n                              {/* Stock quantity */}\n                              <span className=\"text-xs text-gray-500 mt-1\">\n                                {product.stock?.quantity || 0} units available\n                              </span>\n                            </div>\n                            <button\n                              onClick={(e) => handleAddToCart(product, e)}\n                              disabled={(product.stock?.quantity || 0) === 0}\n                              className={`p-2 rounded-lg transition-colors ${\n                                (product.stock?.quantity || 0) === 0\n                                  ? 'bg-gray-400 text-gray-600 cursor-not-allowed'\n                                  : 'bg-green-600 text-white hover:bg-green-700'\n                              }`}\n                              title={(product.stock?.quantity || 0) === 0 ? 'Out of Stock' : 'Add to Cart'}\n                            >\n                              <FiShoppingCart className=\"w-4 h-4\" />\n                            </button>\n                          </div>\n                        </div>\n                      </Link>\n                    </div>\n                  ))}\n                </div>\n\n                {/* Pagination */}\n                {totalPages > 1 && (\n                  <div className=\"flex justify-center mt-8\">\n                    <div className=\"flex space-x-2\">\n                      {[...Array(totalPages)].map((_, i) => (\n                        <button\n                          key={i + 1}\n                          onClick={() => handlePageChange(i + 1)}\n                          className={`px-3 py-2 rounded-lg ${\n                            currentPage === i + 1\n                              ? 'bg-green-600 text-white'\n                              : 'bg-white text-gray-700 hover:bg-gray-100'\n                          }`}\n                        >\n                          {i + 1}\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                )}\n              </>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Products;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,eAAe,EAAEC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACrE,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,cAAc,QAAQ,gBAAgB;AACjF,SAASC,WAAW,EAAEC,UAAU,QAAQ,8BAA8B;AACtE,SAASC,SAAS,EAAEC,OAAO,QAAQ,2BAA2B;AAC9D,SAASC,WAAW,EAAEC,2BAA2B,QAAQ,mBAAmB;AAC5E,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EACrB,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGxB,eAAe,CAAC,CAAC;EACzD,MAAM,CAACyB,QAAQ,EAAEC,WAAW,CAAC,GAAG7B,QAAQ,CAAC,MAAM,CAAC;EAChD,MAAM,CAAC8B,WAAW,EAAEC,cAAc,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAE6B,QAAQ;IAAEC,SAAS;IAAEC,UAAU;IAAEC,WAAW;IAAEC;EAAQ,CAAC,GAAGpC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACL,QAAQ,CAAC;EACxG,MAAM;IAAEM;EAAW,CAAC,GAAGtC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACC,UAAU,CAAC;EAC/D,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGxC,WAAW,CAAEqC,KAAK,IAAKA,KAAK,CAACI,IAAI,CAAC;EAE1D5C,SAAS,CAAC,MAAM;IACd,MAAM6C,MAAM,GAAG;MACbC,IAAI,EAAEnB,YAAY,CAACoB,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;MACnCC,QAAQ,EAAErB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CE,MAAM,EAAEtB,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxCG,QAAQ,EAAEvB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CI,QAAQ,EAAExB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;MAC5CK,MAAM,EAAEzB,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;MACxCM,MAAM,EAAE1B,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,WAAW;MACjDO,SAAS,EAAE3B,YAAY,CAACoB,GAAG,CAAC,WAAW,CAAC,IAAI;IAC9C,CAAC;IAEDd,QAAQ,CAACpB,UAAU,CAACgC,MAAM,CAAC,CAAC;IAC5BZ,QAAQ,CAACrB,WAAW,CAACiC,MAAM,CAAC,CAAC;EAC/B,CAAC,EAAE,CAAClB,YAAY,EAAEM,QAAQ,CAAC,CAAC;EAE5B,MAAMsB,kBAAkB,GAAGA,CAACC,GAAG,EAAEC,KAAK,KAAK;IACzC,MAAMC,SAAS,GAAG,IAAIC,eAAe,CAAChC,YAAY,CAAC;IACnD,IAAI8B,KAAK,EAAE;MACTC,SAAS,CAACE,GAAG,CAACJ,GAAG,EAAEC,KAAK,CAAC;IAC3B,CAAC,MAAM;MACLC,SAAS,CAACG,MAAM,CAACL,GAAG,CAAC;IACvB;IACAE,SAAS,CAACE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC5BhC,eAAe,CAAC8B,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMI,gBAAgB,GAAIhB,IAAI,IAAK;IACjC,MAAMY,SAAS,GAAG,IAAIC,eAAe,CAAChC,YAAY,CAAC;IACnD+B,SAAS,CAACE,GAAG,CAAC,MAAM,EAAEd,IAAI,CAAC;IAC3BlB,eAAe,CAAC8B,SAAS,CAAC;EAC5B,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzBnC,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMoC,eAAe,GAAG,MAAAA,CAAOC,OAAO,EAAEC,CAAC,KAAK;IAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBD,CAAC,CAACE,eAAe,CAAC,CAAC;IAEnB,IAAI,CAAC1B,IAAI,IAAI,CAACC,KAAK,EAAE;MACnBxB,KAAK,CAACkD,KAAK,CAAC,mCAAmC,CAAC;MAChDnC,QAAQ,CAAC,QAAQ,CAAC;MAClB;IACF;IAEA,IAAI;MACF,MAAMD,QAAQ,CAACnB,SAAS,CAAC;QACvBwD,SAAS,EAAEL,OAAO,CAACM,GAAG;QACtBC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MAEZtD,KAAK,CAACuD,OAAO,CAAC,GAAGT,OAAO,CAACU,IAAI,iBAAiB,CAAC;;MAE/C;MACA1C,QAAQ,CAAClB,OAAO,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOsD,KAAK,EAAE;MACdlD,KAAK,CAACkD,KAAK,CAAC,4BAA4B,CAAC;MACzCO,OAAO,CAACP,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,oBACEhD,OAAA;IAAKwD,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCzD,OAAA;MAAKwD,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DzD,OAAA;QAAKwD,SAAS,EAAC,4EAA4E;QAAAC,QAAA,gBACzFzD,OAAA;UAAAyD,QAAA,gBACEzD,OAAA;YAAIwD,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnE7D,OAAA;YAAGwD,SAAS,EAAC,eAAe;YAAAC,QAAA,GACzBnD,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,IAAI,uBAAuBpB,YAAY,CAACoB,GAAG,CAAC,QAAQ,CAAC,GAAG,EAClFpB,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,IAAI,cAAArB,gBAAA,GAAae,UAAU,CAAC0C,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACb,GAAG,KAAK5C,YAAY,CAACoB,GAAG,CAAC,UAAU,CAAC,CAAC,cAAArB,gBAAA,uBAA5DA,gBAAA,CAA8DiD,IAAI,EAAE;UAAA;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN7D,OAAA;UAAKwD,SAAS,EAAC,0CAA0C;UAAAC,QAAA,gBAEvDzD,OAAA;YAAKwD,SAAS,EAAC,iCAAiC;YAAAC,QAAA,gBAC9CzD,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAMvD,WAAW,CAAC,MAAM,CAAE;cACnC+C,SAAS,EAAE,OAAOhD,QAAQ,KAAK,MAAM,GAAG,yBAAyB,GAAG,eAAe,eAAgB;cAAAiD,QAAA,eAEnGzD,OAAA,CAACd,MAAM;gBAACsE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACT7D,OAAA;cACEgE,OAAO,EAAEA,CAAA,KAAMvD,WAAW,CAAC,MAAM,CAAE;cACnC+C,SAAS,EAAE,OAAOhD,QAAQ,KAAK,MAAM,GAAG,yBAAyB,GAAG,eAAe,eAAgB;cAAAiD,QAAA,eAEnGzD,OAAA,CAACb,MAAM;gBAACqE,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7D,OAAA;YACEgE,OAAO,EAAEA,CAAA,KAAMrD,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5C8C,SAAS,EAAC,mFAAmF;YAAAC,QAAA,gBAE7FzD,OAAA,CAACZ,QAAQ;cAACoE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC7D,OAAA;cAAAyD,QAAA,EAAM;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eAGT7D,OAAA;YACEoC,KAAK,EAAE,GAAGlB,OAAO,CAACc,MAAM,IAAId,OAAO,CAACe,SAAS,EAAG;YAChDgC,QAAQ,EAAGpB,CAAC,IAAK;cACf,MAAM,CAACb,MAAM,EAAEC,SAAS,CAAC,GAAGY,CAAC,CAACqB,MAAM,CAAC9B,KAAK,CAAC+B,KAAK,CAAC,GAAG,CAAC;cACrDjC,kBAAkB,CAAC,QAAQ,EAAEF,MAAM,CAAC;cACpCE,kBAAkB,CAAC,WAAW,EAAED,SAAS,CAAC;YAC5C,CAAE;YACFuB,SAAS,EAAC,2FAA2F;YAAAC,QAAA,gBAErGzD,OAAA;cAAQoC,KAAK,EAAC,gBAAgB;cAAAqB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACpD7D,OAAA;cAAQoC,KAAK,EAAC,eAAe;cAAAqB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnD7D,OAAA;cAAQoC,KAAK,EAAC,WAAW;cAAAqB,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrD7D,OAAA;cAAQoC,KAAK,EAAC,YAAY;cAAAqB,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACtD7D,OAAA;cAAQoC,KAAK,EAAC,UAAU;cAAAqB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9C7D,OAAA;cAAQoC,KAAK,EAAC,WAAW;cAAAqB,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/C7D,OAAA;cAAQoC,KAAK,EAAC,qBAAqB;cAAAqB,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,GAE7C/C,WAAW,iBACVV,OAAA;UAAKwD,SAAS,EAAC,2CAA2C;UAAAC,QAAA,gBACxDzD,OAAA;YAAKwD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDzD,OAAA;cAAIwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClD7D,OAAA;cACEgE,OAAO,EAAEtB,YAAa;cACtBc,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EACxD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN7D,OAAA;YAAKwD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzD,OAAA;cAAIwD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChD7D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBrC,UAAU,CAACgD,GAAG,CAAEzC,QAAQ,iBACvB3B,OAAA;gBAA0BwD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACrDzD,OAAA;kBACEqE,IAAI,EAAC,OAAO;kBACZf,IAAI,EAAC,UAAU;kBACflB,KAAK,EAAET,QAAQ,CAACuB,GAAI;kBACpBoB,OAAO,EAAEpD,OAAO,CAACS,QAAQ,KAAKA,QAAQ,CAACuB,GAAI;kBAC3Ce,QAAQ,EAAGpB,CAAC,IAAKX,kBAAkB,CAAC,UAAU,EAAEW,CAAC,CAACqB,MAAM,CAAC9B,KAAK,CAAE;kBAChEoB,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACF7D,OAAA;kBAAMwD,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAE9B,QAAQ,CAAC2B;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAT3ClC,QAAQ,CAACuB,GAAG;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAUjB,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAKwD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzD,OAAA;cAAIwD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjD7D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBzD,OAAA;gBACEqE,IAAI,EAAC,QAAQ;gBACbE,WAAW,EAAC,WAAW;gBACvBnC,KAAK,EAAElB,OAAO,CAACW,QAAS;gBACxBoC,QAAQ,EAAGpB,CAAC,IAAKX,kBAAkB,CAAC,UAAU,EAAEW,CAAC,CAACqB,MAAM,CAAC9B,KAAK,CAAE;gBAChEoB,SAAS,EAAC;cAAyF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC,eACF7D,OAAA;gBACEqE,IAAI,EAAC,QAAQ;gBACbE,WAAW,EAAC,WAAW;gBACvBnC,KAAK,EAAElB,OAAO,CAACY,QAAS;gBACxBmC,QAAQ,EAAGpB,CAAC,IAAKX,kBAAkB,CAAC,UAAU,EAAEW,CAAC,CAACqB,MAAM,CAAC9B,KAAK,CAAE;gBAChEoB,SAAS,EAAC;cAAyF;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7D,OAAA;YAAKwD,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzD,OAAA;cAAIwD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5C7D,OAAA;cAAKwD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACW,GAAG,CAAErC,MAAM,iBACvB/B,OAAA;gBAAoBwD,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC/CzD,OAAA;kBACEqE,IAAI,EAAC,OAAO;kBACZf,IAAI,EAAC,QAAQ;kBACblB,KAAK,EAAEL,MAAO;kBACduC,OAAO,EAAEpD,OAAO,CAACa,MAAM,KAAKA,MAAM,CAACyC,QAAQ,CAAC,CAAE;kBAC9CP,QAAQ,EAAGpB,CAAC,IAAKX,kBAAkB,CAAC,QAAQ,EAAEW,CAAC,CAACqB,MAAM,CAAC9B,KAAK,CAAE;kBAC9DoB,SAAS,EAAC;gBAAqC;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC,eACF7D,OAAA;kBAAKwD,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,GACpC,CAAC,GAAGgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,kBACtB3E,OAAA,CAACX,MAAM;oBAELmE,SAAS,EAAE,WACTmB,CAAC,GAAG5C,MAAM,GAAG,8BAA8B,GAAG,eAAe;kBAC5D,GAHE4C,CAAC;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAIP,CACF,CAAC,eACF7D,OAAA;oBAAMwD,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC;cAAA,GAnBI9B,MAAM;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBX,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGD7D,OAAA;UAAKwD,SAAS,EAAC,QAAQ;UAAAC,QAAA,EACpB1C,SAAS,gBACRf,OAAA;YAAKwD,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxCzD,OAAA,CAACH,cAAc;cAAC+E,IAAI,EAAC;YAAI;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,GACJ/C,QAAQ,CAAC+D,MAAM,KAAK,CAAC,gBACvB7E,OAAA;YAAKwD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCzD,OAAA;cAAGwD,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAC1D7D,OAAA;cACEgE,OAAO,EAAEtB,YAAa;cACtBc,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,gBAEN7D,OAAA,CAAAE,SAAA;YAAAuD,QAAA,gBACEzD,OAAA;cAAKwD,SAAS,EAAE,cACdhD,QAAQ,KAAK,MAAM,GACf,0DAA0D,GAC1D,aAAa,EAChB;cAAAiD,QAAA,EACA3C,QAAQ,CAACsD,GAAG,CAAExB,OAAO;gBAAA,IAAAkC,eAAA,EAAAC,gBAAA,EAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,eAAA;gBAAA,oBACpBxF,OAAA;kBAEEwD,SAAS,EAAE,mEACThD,QAAQ,KAAK,MAAM,GAAG,MAAM,GAAG,EAAE,EAChC;kBAAAiD,QAAA,eAEHzD,OAAA,CAAChB,IAAI;oBAACyG,EAAE,EAAE,aAAa7C,OAAO,CAACM,GAAG,EAAG;oBAACM,SAAS,EAAEhD,QAAQ,KAAK,MAAM,GAAG,aAAa,GAAG,EAAG;oBAAAiD,QAAA,gBACxFzD,OAAA;sBAAKwD,SAAS,EAAE,YAAYhD,QAAQ,KAAK,MAAM,GAAG,oBAAoB,GAAG,EAAE,EAAG;sBAAAiD,QAAA,gBAC5EzD,OAAA;wBACE0F,GAAG,EAAE,EAAAZ,eAAA,GAAAlC,OAAO,CAAC+C,MAAM,cAAAb,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,uBAAnBA,gBAAA,CAAqBa,GAAG,KAAI,0BAA2B;wBAC5DC,GAAG,EAAEjD,OAAO,CAACU,IAAK;wBAClBE,SAAS,EAAE,uBACThD,QAAQ,KAAK,MAAM,GAAG,mBAAmB,GAAG,mBAAmB;sBAC9D;wBAAAkD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,EAEDjB,OAAO,CAACkD,aAAa,IAAIlD,OAAO,CAACkD,aAAa,GAAGlD,OAAO,CAACmD,KAAK,iBAC7D/F,OAAA;wBAAKwD,SAAS,EAAC,oFAAoF;wBAAAC,QAAA,GAChG7D,2BAA2B,CAACgD,OAAO,CAACkD,aAAa,EAAElD,OAAO,CAACmD,KAAK,CAAC,EAAC,OACrE;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CACN,EAEA,CAAC,EAAAmB,cAAA,GAAApC,OAAO,CAACoD,KAAK,cAAAhB,cAAA,uBAAbA,cAAA,CAAe7B,QAAQ,KAAI,CAAC,MAAM,CAAC,iBACnCnD,OAAA;wBAAKwD,SAAS,EAAC,uFAAuF;wBAAAC,QAAA,eACpGzD,OAAA;0BAAMwD,SAAS,EAAC,8BAA8B;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CACN;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACN7D,OAAA;sBAAKwD,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACzBzD,OAAA;wBAAKwD,SAAS,EAAC,uCAAuC;wBAAAC,QAAA,gBACpDzD,OAAA;0BAAIwD,SAAS,EAAC,4EAA4E;0BAAAC,QAAA,EACvFb,OAAO,CAACU;wBAAI;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACX,CAAC,eAEL7D,OAAA;0BAAMwD,SAAS,EAAE,gDACf,CAAC,EAAAyB,eAAA,GAAArC,OAAO,CAACoD,KAAK,cAAAf,eAAA,uBAAbA,eAAA,CAAe9B,QAAQ,KAAI,CAAC,IAAI,CAAC,GAC9B,6BAA6B,GAC7B,yBAAyB,EAC5B;0BAAAM,QAAA,EACA,CAAC,EAAAyB,eAAA,GAAAtC,OAAO,CAACoD,KAAK,cAAAd,eAAA,uBAAbA,eAAA,CAAe/B,QAAQ,KAAI,CAAC,IAAI,CAAC,GAAG,UAAU,GAAG;wBAAc;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7D,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eAGN7D,OAAA;wBAAKwD,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,GAC9Cb,OAAO,CAACqD,KAAK,iBACZjG,OAAA;0BAAMwD,SAAS,EAAC,qDAAqD;0BAAAC,QAAA,EAClEb,OAAO,CAACqD;wBAAK;0BAAAvC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACV,CACP,EACA,EAAAsB,iBAAA,GAAAvC,OAAO,CAACjB,QAAQ,cAAAwD,iBAAA,uBAAhBA,iBAAA,CAAkB7B,IAAI,kBACrBtD,OAAA;0BAAMwD,SAAS,EAAC,oDAAoD;0BAAAC,QAAA,EACjEb,OAAO,CAACjB,QAAQ,CAAC2B;wBAAI;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClB,CACP;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,EAELrD,QAAQ,KAAK,MAAM,iBAClBR,OAAA;wBAAGwD,SAAS,EAAC,yCAAyC;wBAAAC,QAAA,EACnDb,OAAO,CAACsD;sBAAW;wBAAAxC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnB,CACJ,eACD7D,OAAA;wBAAKwD,SAAS,EAAC,wBAAwB;wBAAAC,QAAA,gBACrCzD,OAAA;0BAAKwD,SAAS,EAAC,mBAAmB;0BAAAC,QAAA,EAC/B,CAAC,GAAGgB,KAAK,CAAC,CAAC,CAAC,CAAC,CAACL,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC;4BAAA,IAAAwB,eAAA;4BAAA,oBACtBnG,OAAA,CAACX,MAAM;8BAELmE,SAAS,EAAE,WACTmB,CAAC,GAAGyB,IAAI,CAACC,KAAK,CAAC,EAAAF,eAAA,GAAAvD,OAAO,CAACb,MAAM,cAAAoE,eAAA,uBAAdA,eAAA,CAAgBG,OAAO,KAAI,CAAC,CAAC,GACxC,8BAA8B,GAC9B,eAAe;4BAClB,GALE3B,CAAC;8BAAAjB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAMP,CAAC;0BAAA,CACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC,eACN7D,OAAA;0BAAMwD,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,GAAC,GAC1C,EAAC,EAAA2B,gBAAA,GAAAxC,OAAO,CAACb,MAAM,cAAAqD,gBAAA,uBAAdA,gBAAA,CAAgBmB,KAAK,KAAI,CAAC,EAAC,GAC/B;wBAAA;0BAAA7C,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACN7D,OAAA;wBAAKwD,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,gBAChDzD,OAAA;0BAAKwD,SAAS,EAAC,eAAe;0BAAAC,QAAA,gBAC5BzD,OAAA;4BAAKwD,SAAS,EAAC,6BAA6B;4BAAAC,QAAA,gBAC1CzD,OAAA;8BAAMwD,SAAS,EAAC,kCAAkC;8BAAAC,QAAA,EAC/C9D,WAAW,CAACiD,OAAO,CAACmD,KAAK;4BAAC;8BAAArC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACvB,CAAC,EACNjB,OAAO,CAACkD,aAAa,IAAIlD,OAAO,CAACkD,aAAa,GAAGlD,OAAO,CAACmD,KAAK,iBAC7D/F,OAAA,CAAAE,SAAA;8BAAAuD,QAAA,gBACEzD,OAAA;gCAAMwD,SAAS,EAAC,oCAAoC;gCAAAC,QAAA,EACjD9D,WAAW,CAACiD,OAAO,CAACkD,aAAa;8BAAC;gCAAApC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAC/B,CAAC,eACP7D,OAAA;gCAAMwD,SAAS,EAAC,oEAAoE;gCAAAC,QAAA,GACjF7D,2BAA2B,CAACgD,OAAO,CAACkD,aAAa,EAAElD,OAAO,CAACmD,KAAK,CAAC,EAAC,OACrE;8BAAA;gCAAArC,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC;4BAAA,eACP,CACH;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACE,CAAC,eAEN7D,OAAA;4BAAMwD,SAAS,EAAC,4BAA4B;4BAAAC,QAAA,GACzC,EAAA4B,eAAA,GAAAzC,OAAO,CAACoD,KAAK,cAAAX,eAAA,uBAAbA,eAAA,CAAelC,QAAQ,KAAI,CAAC,EAAC,kBAChC;0BAAA;4BAAAO,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACN7D,OAAA;0BACEgE,OAAO,EAAGnB,CAAC,IAAKF,eAAe,CAACC,OAAO,EAAEC,CAAC,CAAE;0BAC5C2D,QAAQ,EAAE,CAAC,EAAAlB,eAAA,GAAA1C,OAAO,CAACoD,KAAK,cAAAV,eAAA,uBAAbA,eAAA,CAAenC,QAAQ,KAAI,CAAC,MAAM,CAAE;0BAC/CK,SAAS,EAAE,oCACT,CAAC,EAAA+B,eAAA,GAAA3C,OAAO,CAACoD,KAAK,cAAAT,eAAA,uBAAbA,eAAA,CAAepC,QAAQ,KAAI,CAAC,MAAM,CAAC,GAChC,8CAA8C,GAC9C,4CAA4C,EAC/C;0BACHsD,KAAK,EAAE,CAAC,EAAAjB,eAAA,GAAA5C,OAAO,CAACoD,KAAK,cAAAR,eAAA,uBAAbA,eAAA,CAAerC,QAAQ,KAAI,CAAC,MAAM,CAAC,GAAG,cAAc,GAAG,aAAc;0BAAAM,QAAA,eAE7EzD,OAAA,CAACV,cAAc;4BAACkE,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAlHFjB,OAAO,CAACM,GAAG;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmHb,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAGL7C,UAAU,GAAG,CAAC,iBACbhB,OAAA;cAAKwD,SAAS,EAAC,0BAA0B;cAAAC,QAAA,eACvCzD,OAAA;gBAAKwD,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B,CAAC,GAAGgB,KAAK,CAACzD,UAAU,CAAC,CAAC,CAACoD,GAAG,CAAC,CAACM,CAAC,EAAEC,CAAC,kBAC/B3E,OAAA;kBAEEgE,OAAO,EAAEA,CAAA,KAAMvB,gBAAgB,CAACkC,CAAC,GAAG,CAAC,CAAE;kBACvCnB,SAAS,EAAE,wBACTvC,WAAW,KAAK0D,CAAC,GAAG,CAAC,GACjB,yBAAyB,GACzB,0CAA0C,EAC7C;kBAAAlB,QAAA,EAEFkB,CAAC,GAAG;gBAAC,GARDA,CAAC,GAAG,CAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OASJ,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA,eACD;QACH;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzD,EAAA,CAxYID,QAAQ;EAAA,QAC4BpB,eAAe,EAItCF,WAAW,EACXI,WAAW,EACsCH,WAAW,EACtDA,WAAW,EACVA,WAAW;AAAA;AAAA4H,EAAA,GAT/BvG,QAAQ;AA0Yd,eAAeA,QAAQ;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}