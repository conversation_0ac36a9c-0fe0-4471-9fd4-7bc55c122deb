import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getFeaturedProducts } from '../store/slices/productSlice';
import productService from '../services/productService';

const Debug = () => {
  const dispatch = useDispatch();
  const { featuredProducts, isLoading, isError, message } = useSelector((state) => state.products);
  const [apiTest, setApiTest] = useState(null);
  const [logs, setLogs] = useState([]);

  const addLog = (message) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  useEffect(() => {
    addLog('Component mounted');
    
    // Test direct API call
    const testAPI = async () => {
      try {
        addLog('Testing direct API call...');
        const result = await productService.getFeaturedProducts();
        addLog(`Direct API success: ${result.products?.length} products`);
        setApiTest(result);
      } catch (error) {
        addLog(`Direct API error: ${error.message}`);
        setApiTest({ error: error.message });
      }
    };

    testAPI();

    // Test Redux action
    addLog('Dispatching getFeaturedProducts action...');
    dispatch(getFeaturedProducts());
  }, [dispatch]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Debug Page</h1>
        
        {/* Logs */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Debug Logs</h2>
          <div className="bg-gray-100 p-4 rounded-lg max-h-64 overflow-y-auto">
            {logs.map((log, index) => (
              <div key={index} className="text-sm font-mono mb-1">{log}</div>
            ))}
          </div>
        </div>

        {/* Redux State */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Redux State</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">State Values:</h3>
              <ul className="text-sm space-y-1">
                <li><strong>isLoading:</strong> {isLoading ? 'true' : 'false'}</li>
                <li><strong>isError:</strong> {isError ? 'true' : 'false'}</li>
                <li><strong>message:</strong> {message || 'none'}</li>
                <li><strong>featuredProducts length:</strong> {featuredProducts?.length || 0}</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">Featured Products:</h3>
              <div className="bg-gray-100 p-2 rounded text-xs max-h-32 overflow-y-auto">
                <pre>{JSON.stringify(featuredProducts?.slice(0, 2), null, 2)}</pre>
              </div>
            </div>
          </div>
        </div>

        {/* Direct API Test */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Direct API Test</h2>
          {apiTest ? (
            <div>
              {apiTest.error ? (
                <div className="text-red-600">
                  <strong>Error:</strong> {apiTest.error}
                </div>
              ) : (
                <div>
                  <p><strong>Success:</strong> {apiTest.success ? 'true' : 'false'}</p>
                  <p><strong>Count:</strong> {apiTest.count}</p>
                  <p><strong>Products:</strong> {apiTest.products?.length}</p>
                  <div className="mt-4">
                    <h3 className="font-medium mb-2">Sample Products:</h3>
                    <div className="space-y-2">
                      {apiTest.products?.slice(0, 3).map((product, index) => (
                        <div key={index} className="bg-gray-100 p-2 rounded">
                          <strong>{product.name}</strong> - ₹{product.price}
                          <br />
                          <small>ID: {product._id}</small>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div>Loading API test...</div>
          )}
        </div>

        {/* Product Display Test */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">Product Display Test</h2>
          {isLoading ? (
            <div>Loading products...</div>
          ) : featuredProducts && featuredProducts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {featuredProducts.slice(0, 6).map((product) => (
                <div key={product._id} className="border rounded-lg p-4">
                  <img
                    src={product.images?.[0]?.url || '/api/placeholder/200/200'}
                    alt={product.name}
                    className="w-full h-32 object-cover rounded mb-2"
                  />
                  <h3 className="font-medium">{product.name}</h3>
                  <p className="text-green-600 font-bold">₹{product.price}</p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-red-600">
              No products to display. 
              <br />
              Featured products length: {featuredProducts?.length || 0}
              <br />
              Is loading: {isLoading ? 'Yes' : 'No'}
              <br />
              Is error: {isError ? 'Yes' : 'No'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Debug;
