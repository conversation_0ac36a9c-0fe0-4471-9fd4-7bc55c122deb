const Order = require('../models/Order');
const Cart = require('../models/Cart');
const Product = require('../models/Product');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Get user orders
// @route   GET /api/orders
// @access  Private
const getOrders = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, status } = req.query;

  const query = { user: req.user._id };
  
  // Filter by status
  if (status) {
    query.orderStatus = status;
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const orders = await Order.find(query)
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .populate('items.product', 'name images slug')
    .select('-paymentInfo.transactionId -paymentInfo.paymentIntentId');

  const total = await Order.countDocuments(query);

  res.status(200).json({
    success: true,
    count: orders.length,
    total,
    totalPages: Math.ceil(total / parseInt(limit)),
    currentPage: parseInt(page),
    orders
  });
});

// @desc    Get single order
// @route   GET /api/orders/:id
// @access  Private
const getOrder = asyncHandler(async (req, res, next) => {
  const order = await Order.findById(req.params.id)
    .populate('items.product', 'name images slug')
    .populate('user', 'firstName lastName email');

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Check if user owns this order or is admin
  if (order.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to access this order', 403));
  }

  res.status(200).json({
    success: true,
    order
  });
});

// @desc    Get order by order number
// @route   GET /api/orders/number/:orderNumber
// @access  Private
const getOrderByNumber = asyncHandler(async (req, res, next) => {
  const order = await Order.findOne({ orderNumber: req.params.orderNumber })
    .populate('items.product', 'name images slug')
    .populate('user', 'firstName lastName email');

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Check if user owns this order or is admin
  if (order.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to access this order', 403));
  }

  res.status(200).json({
    success: true,
    order
  });
});

// @desc    Create order (Cash on Delivery)
// @route   POST /api/orders
// @access  Private
const createOrder = asyncHandler(async (req, res, next) => {
  const { shippingAddress, billingAddress, paymentMethod = 'cash_on_delivery' } = req.body;

  // Get user cart
  const cart = await Cart.getCartWithProducts(req.user._id);
  if (!cart || cart.items.length === 0) {
    return next(new ErrorResponse('Cart is empty', 400));
  }

  // Validate cart items
  const { validItems, invalidItems } = await cart.validateItems();
  if (invalidItems.length > 0) {
    return next(new ErrorResponse('Some items in your cart are no longer available', 400));
  }

  // Calculate order summary
  const subtotal = cart.totalPrice;
  const tax = subtotal * 0.08; // 8% tax rate
  const shipping = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
  const discount = cart.discountAmount;
  const total = subtotal + tax + shipping - discount;

  // Create order
  const order = new Order({
    user: req.user._id,
    items: cart.items.map(item => ({
      product: item.product._id,
      name: item.product.name,
      price: item.price,
      quantity: item.quantity,
      image: item.product.images[0]?.url || '',
      sku: item.product.sku
    })),
    orderSummary: {
      subtotal,
      tax,
      shipping,
      discount,
      total
    },
    appliedCoupons: cart.appliedCoupons,
    shippingAddress,
    billingAddress: billingAddress || { ...shippingAddress, sameAsShipping: true },
    paymentInfo: {
      method: paymentMethod,
      status: paymentMethod === 'cash_on_delivery' ? 'pending' : 'completed'
    },
    orderStatus: 'confirmed'
  });

  await order.save();

  // Update product stock
  for (const item of cart.items) {
    const product = await Product.findById(item.product._id);
    if (product && product.stock.trackInventory) {
      product.stock.quantity -= item.quantity;
      await product.save();
    }
  }

  // Mark applied coupons as used
  for (const appliedCoupon of cart.appliedCoupons) {
    const Coupon = require('../models/Coupon');
    const coupon = await Coupon.findByCode(appliedCoupon.code);
    if (coupon) {
      await coupon.markAsUsed(req.user._id, order.orderNumber);
    }
  }

  // Clear cart
  await cart.clearCart();

  // Send order confirmation email
  try {
    const nodemailer = require('nodemailer');
    const transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    await transporter.sendMail({
      from: process.env.EMAIL_USER,
      to: req.user.email,
      subject: `Order Confirmation - ${order.orderNumber}`,
      html: `
        <h1>Order Confirmation</h1>
        <p>Thank you for your order!</p>
        <p><strong>Order Number:</strong> ${order.orderNumber}</p>
        <p><strong>Total:</strong> $${total.toFixed(2)}</p>
        <p><strong>Payment Method:</strong> ${paymentMethod === 'cash_on_delivery' ? 'Cash on Delivery' : 'Card Payment'}</p>
        <p>We'll send you another email when your order ships.</p>
      `
    });
  } catch (emailError) {
    console.error('Order confirmation email failed:', emailError);
    // Don't fail the order if email fails
  }

  res.status(201).json({
    success: true,
    message: 'Order created successfully',
    order: {
      orderNumber: order.orderNumber,
      total: order.orderSummary.total,
      status: order.orderStatus,
      estimatedDelivery: order.estimatedDeliveryDate
    }
  });
});

// @desc    Cancel order
// @route   PUT /api/orders/:id/cancel
// @access  Private
const cancelOrder = asyncHandler(async (req, res, next) => {
  const { reason } = req.body;

  const order = await Order.findById(req.params.id);
  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Check if user owns this order
  if (order.user.toString() !== req.user._id.toString()) {
    return next(new ErrorResponse('Not authorized to cancel this order', 403));
  }

  // Check if order can be cancelled
  if (!['confirmed', 'processing'].includes(order.orderStatus)) {
    return next(new ErrorResponse('Order cannot be cancelled at this stage', 400));
  }

  // Cancel order
  await order.cancelOrder(reason || 'Cancelled by customer');

  // Restore product stock
  for (const item of order.items) {
    const product = await Product.findById(item.product);
    if (product && product.stock.trackInventory) {
      product.stock.quantity += item.quantity;
      await product.save();
    }
  }

  res.status(200).json({
    success: true,
    message: 'Order cancelled successfully',
    order: {
      orderNumber: order.orderNumber,
      status: order.orderStatus
    }
  });
});

// @desc    Track order
// @route   GET /api/orders/:id/track
// @access  Private
const trackOrder = asyncHandler(async (req, res, next) => {
  const order = await Order.findById(req.params.id)
    .select('orderNumber orderStatus statusHistory trackingNumber estimatedDeliveryDate');

  if (!order) {
    return next(new ErrorResponse('Order not found', 404));
  }

  // Check if user owns this order or is admin
  if (order.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
    return next(new ErrorResponse('Not authorized to track this order', 403));
  }

  res.status(200).json({
    success: true,
    tracking: {
      orderNumber: order.orderNumber,
      currentStatus: order.orderStatus,
      trackingNumber: order.trackingNumber,
      estimatedDelivery: order.estimatedDeliveryDate,
      statusHistory: order.statusHistory
    }
  });
});

// @desc    Get order statistics for user
// @route   GET /api/orders/stats
// @access  Private
const getOrderStats = asyncHandler(async (req, res, next) => {
  const stats = await Order.aggregate([
    { $match: { user: req.user._id } },
    {
      $group: {
        _id: null,
        totalOrders: { $sum: 1 },
        totalSpent: { $sum: '$orderSummary.total' },
        averageOrderValue: { $avg: '$orderSummary.total' },
        statusBreakdown: {
          $push: '$orderStatus'
        }
      }
    }
  ]);

  const statusCounts = {};
  if (stats.length > 0) {
    stats[0].statusBreakdown.forEach(status => {
      statusCounts[status] = (statusCounts[status] || 0) + 1;
    });
  }

  res.status(200).json({
    success: true,
    stats: stats.length > 0 ? {
      totalOrders: stats[0].totalOrders,
      totalSpent: stats[0].totalSpent,
      averageOrderValue: stats[0].averageOrderValue,
      statusBreakdown: statusCounts
    } : {
      totalOrders: 0,
      totalSpent: 0,
      averageOrderValue: 0,
      statusBreakdown: {}
    }
  });
});

module.exports = {
  getOrders,
  getOrder,
  getOrderByNumber,
  createOrder,
  cancelOrder,
  trackOrder,
  getOrderStats
};
