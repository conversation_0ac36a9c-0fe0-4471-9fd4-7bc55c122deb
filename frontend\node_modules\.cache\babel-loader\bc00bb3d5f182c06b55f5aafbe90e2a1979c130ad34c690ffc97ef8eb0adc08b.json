{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\components\\\\auth\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children,\n  adminOnly = false\n}) => {\n  _s();\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const location = useLocation();\n  if (!user || !token) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/login\",\n      state: {\n        from: location\n      },\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 12\n    }, this);\n  }\n  if (adminOnly && user.role !== 'admin') {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 12\n    }, this);\n  }\n  return children;\n};\n_s(ProtectedRoute, \"fjjMcPcT7iY1Lq+xahhWpv5Jkdc=\", false, function () {\n  return [useSelector, useLocation];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useSelector", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "adminOnly", "_s", "user", "token", "state", "auth", "location", "to", "from", "replace", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/components/auth/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\n\nconst ProtectedRoute = ({ children, adminOnly = false }) => {\n  const { user, token } = useSelector((state) => state.auth);\n  const location = useLocation();\n\n  if (!user || !token) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  if (adminOnly && user.role !== 'admin') {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  return children;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,WAAW,QAAQ,kBAAkB;AACxD,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAMC,cAAc,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,SAAS,GAAG;AAAM,CAAC,KAAK;EAAAC,EAAA;EAC1D,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGR,WAAW,CAAES,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC1D,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,IAAI,CAACQ,IAAI,IAAI,CAACC,KAAK,EAAE;IACnB,oBAAON,OAAA,CAACJ,QAAQ;MAACc,EAAE,EAAC,QAAQ;MAACH,KAAK,EAAE;QAAEI,IAAI,EAAEF;MAAS,CAAE;MAACG,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpE;EAEA,IAAIb,SAAS,IAAIE,IAAI,CAACY,IAAI,KAAK,OAAO,EAAE;IACtC,oBAAOjB,OAAA,CAACJ,QAAQ;MAACc,EAAE,EAAC,GAAG;MAACE,OAAO;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;EAEA,OAAOd,QAAQ;AACjB,CAAC;AAACE,EAAA,CAbIH,cAAc;EAAA,QACMH,WAAW,EAClBD,WAAW;AAAA;AAAAqB,EAAA,GAFxBjB,cAAc;AAepB,eAAeA,cAAc;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}