const set = require('regenerate')(0x5E, 0x60, 0xA8, 0xAF, 0xB4, 0xB8, 0x2ED, 0x375, 0x888, 0x1FBD, 0xAB5B, 0xFF3E, 0xFF40, 0xFFE3);
set.addRange(0x2C2, 0x2C5).addRange(0x2D2, 0x2DF).addRange(0x2E5, 0x2EB).addRange(0x2EF, 0x2FF).addRange(0x384, 0x385).addRange(0x1FBF, 0x1FC1).addRange(0x1FCD, 0x1FCF).addRange(0x1FDD, 0x1FDF).addRange(0x1FED, 0x1FEF).addRange(0x1FFD, 0x1FFE).addRange(0x309B, 0x309C).addRange(0xA700, 0xA716).addRange(0xA720, 0xA721).addRange(0xA789, 0xA78A).addRange(0xAB6A, 0xAB6B).addRange(0xFBB2, 0xFBC2).addRange(0x1F3FB, 0x1F3FF);
exports.characters = set;
