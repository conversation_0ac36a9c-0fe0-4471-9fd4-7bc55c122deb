[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\ErrorBoundary.js": "33"}, {"size": 893, "mtime": 1753959821369, "results": "34", "hashOfConfig": "35"}, {"size": 3500, "mtime": 1754032956261, "results": "36", "hashOfConfig": "35"}, {"size": 649, "mtime": 1753959858771, "results": "37", "hashOfConfig": "35"}, {"size": 9892, "mtime": 1754029263531, "results": "38", "hashOfConfig": "35"}, {"size": 18027, "mtime": 1754031331451, "results": "39", "hashOfConfig": "35"}, {"size": 16029, "mtime": 1754029372102, "results": "40", "hashOfConfig": "35"}, {"size": 520, "mtime": 1753960398266, "results": "41", "hashOfConfig": "35"}, {"size": 11245, "mtime": 1754032561073, "results": "42", "hashOfConfig": "35"}, {"size": 665, "mtime": 1753960412289, "results": "43", "hashOfConfig": "35"}, {"size": 512, "mtime": 1753960404241, "results": "44", "hashOfConfig": "35"}, {"size": 527, "mtime": 1753960421333, "results": "45", "hashOfConfig": "35"}, {"size": 1195, "mtime": 1753960448070, "results": "46", "hashOfConfig": "35"}, {"size": 10860, "mtime": 1753960292711, "results": "47", "hashOfConfig": "35"}, {"size": 8411, "mtime": 1753960255797, "results": "48", "hashOfConfig": "35"}, {"size": 550, "mtime": 1753960427641, "results": "49", "hashOfConfig": "35"}, {"size": 5487, "mtime": 1753959881053, "results": "50", "hashOfConfig": "35"}, {"size": 690, "mtime": 1753960437755, "results": "51", "hashOfConfig": "35"}, {"size": 6556, "mtime": 1753959951852, "results": "52", "hashOfConfig": "35"}, {"size": 2169, "mtime": 1753960017350, "results": "53", "hashOfConfig": "35"}, {"size": 7835, "mtime": 1754032577788, "results": "54", "hashOfConfig": "35"}, {"size": 4356, "mtime": 1753960002763, "results": "55", "hashOfConfig": "35"}, {"size": 10277, "mtime": 1753961863361, "results": "56", "hashOfConfig": "35"}, {"size": 425, "mtime": 1754026881172, "results": "57", "hashOfConfig": "35"}, {"size": 6787, "mtime": 1753961843906, "results": "58", "hashOfConfig": "35"}, {"size": 681, "mtime": 1753960177430, "results": "59", "hashOfConfig": "35"}, {"size": 2367, "mtime": 1753960036985, "results": "60", "hashOfConfig": "35"}, {"size": 1801, "mtime": 1753960046714, "results": "61", "hashOfConfig": "35"}, {"size": 675, "mtime": 1753960072253, "results": "62", "hashOfConfig": "35"}, {"size": 1378, "mtime": 1754032591423, "results": "63", "hashOfConfig": "35"}, {"size": 1313, "mtime": 1753960066278, "results": "64", "hashOfConfig": "35"}, {"size": 931, "mtime": 1753960025458, "results": "65", "hashOfConfig": "35"}, {"size": 1785, "mtime": 1754029155865, "results": "66", "hashOfConfig": "35"}, {"size": 2770, "mtime": 1754032884749, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c2b6v7", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js", ["167", "168"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js", ["169"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js", ["170"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js", ["171", "172"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js", ["173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\ErrorBoundary.js", [], [], {"ruleId": "184", "severity": 1, "message": "185", "line": 11, "column": 8, "nodeType": "186", "messageId": "187", "endLine": 11, "endColumn": 22}, {"ruleId": "184", "severity": 1, "message": "188", "line": 39, "column": 11, "nodeType": "186", "messageId": "187", "endLine": 39, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "189", "line": 6, "column": 23, "nodeType": "186", "messageId": "187", "endLine": 6, "endColumn": 50}, {"ruleId": "184", "severity": 1, "message": "190", "line": 14, "column": 29, "nodeType": "186", "messageId": "187", "endLine": 14, "endColumn": 34}, {"ruleId": "191", "severity": 1, "message": "192", "line": 150, "column": 43, "nodeType": "193", "messageId": "194", "endLine": 150, "endColumn": 44, "suggestions": "195"}, {"ruleId": "191", "severity": 1, "message": "196", "line": 150, "column": 45, "nodeType": "193", "messageId": "194", "endLine": 150, "endColumn": 46, "suggestions": "197"}, {"ruleId": "198", "severity": 1, "message": "199", "line": 23, "column": 15, "nodeType": "200", "endLine": 23, "endColumn": 92}, {"ruleId": "198", "severity": 1, "message": "199", "line": 26, "column": 15, "nodeType": "200", "endLine": 26, "endColumn": 92}, {"ruleId": "198", "severity": 1, "message": "199", "line": 29, "column": 15, "nodeType": "200", "endLine": 29, "endColumn": 92}, {"ruleId": "198", "severity": 1, "message": "199", "line": 72, "column": 17, "nodeType": "200", "endLine": 72, "endColumn": 102}, {"ruleId": "198", "severity": 1, "message": "199", "line": 77, "column": 17, "nodeType": "200", "endLine": 77, "endColumn": 102}, {"ruleId": "198", "severity": 1, "message": "199", "line": 82, "column": 17, "nodeType": "200", "endLine": 82, "endColumn": 102}, {"ruleId": "198", "severity": 1, "message": "199", "line": 87, "column": 17, "nodeType": "200", "endLine": 87, "endColumn": 102}, {"ruleId": "198", "severity": 1, "message": "199", "line": 92, "column": 17, "nodeType": "200", "endLine": 92, "endColumn": 102}, {"ruleId": "198", "severity": 1, "message": "199", "line": 145, "column": 15, "nodeType": "200", "endLine": 145, "endColumn": 100}, {"ruleId": "198", "severity": 1, "message": "199", "line": 148, "column": 15, "nodeType": "200", "endLine": 148, "endColumn": 100}, {"ruleId": "198", "severity": 1, "message": "199", "line": 151, "column": 15, "nodeType": "200", "endLine": 151, "endColumn": 100}, "no-unused-vars", "'LoadingSpinner' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'calculateDiscountPercentage' is defined but never used.", "'error' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["201", "202"], "Unnecessary escape character: \\).", ["203", "204"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"messageId": "205", "fix": "206", "desc": "207"}, {"messageId": "208", "fix": "209", "desc": "210"}, {"messageId": "205", "fix": "211", "desc": "207"}, {"messageId": "208", "fix": "212", "desc": "210"}, "removeEscape", {"range": "213", "text": "214"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "215", "text": "216"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "217", "text": "214"}, {"range": "218", "text": "216"}, [5576, 5577], "", [5576, 5576], "\\", [5578, 5579], [5578, 5578]]