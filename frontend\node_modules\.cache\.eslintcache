[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js": "31"}, {"size": 893, "mtime": 1753959821369, "results": "32", "hashOfConfig": "33"}, {"size": 3390, "mtime": 1753960096440, "results": "34", "hashOfConfig": "33"}, {"size": 649, "mtime": 1753959858771, "results": "35", "hashOfConfig": "33"}, {"size": 9790, "mtime": 1754027914631, "results": "36", "hashOfConfig": "33"}, {"size": 14329, "mtime": 1754028627654, "results": "37", "hashOfConfig": "33"}, {"size": 16121, "mtime": 1754028548677, "results": "38", "hashOfConfig": "33"}, {"size": 520, "mtime": 1753960398266, "results": "39", "hashOfConfig": "33"}, {"size": 11348, "mtime": 1754028022706, "results": "40", "hashOfConfig": "33"}, {"size": 665, "mtime": 1753960412289, "results": "41", "hashOfConfig": "33"}, {"size": 512, "mtime": 1753960404241, "results": "42", "hashOfConfig": "33"}, {"size": 527, "mtime": 1753960421333, "results": "43", "hashOfConfig": "33"}, {"size": 1195, "mtime": 1753960448070, "results": "44", "hashOfConfig": "33"}, {"size": 10860, "mtime": 1753960292711, "results": "45", "hashOfConfig": "33"}, {"size": 8411, "mtime": 1753960255797, "results": "46", "hashOfConfig": "33"}, {"size": 550, "mtime": 1753960427641, "results": "47", "hashOfConfig": "33"}, {"size": 5487, "mtime": 1753959881053, "results": "48", "hashOfConfig": "33"}, {"size": 690, "mtime": 1753960437755, "results": "49", "hashOfConfig": "33"}, {"size": 6556, "mtime": 1753959951852, "results": "50", "hashOfConfig": "33"}, {"size": 2169, "mtime": 1753960017350, "results": "51", "hashOfConfig": "33"}, {"size": 8062, "mtime": 1753959983263, "results": "52", "hashOfConfig": "33"}, {"size": 4356, "mtime": 1753960002763, "results": "53", "hashOfConfig": "33"}, {"size": 10277, "mtime": 1753961863361, "results": "54", "hashOfConfig": "33"}, {"size": 425, "mtime": 1754026881172, "results": "55", "hashOfConfig": "33"}, {"size": 6787, "mtime": 1753961843906, "results": "56", "hashOfConfig": "33"}, {"size": 681, "mtime": 1753960177430, "results": "57", "hashOfConfig": "33"}, {"size": 2367, "mtime": 1753960036985, "results": "58", "hashOfConfig": "33"}, {"size": 1801, "mtime": 1753960046714, "results": "59", "hashOfConfig": "33"}, {"size": 675, "mtime": 1753960072253, "results": "60", "hashOfConfig": "33"}, {"size": 2041, "mtime": 1753960057366, "results": "61", "hashOfConfig": "33"}, {"size": 1313, "mtime": 1753960066278, "results": "62", "hashOfConfig": "33"}, {"size": 931, "mtime": 1753960025458, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c2b6v7", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js", ["157", "158"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js", ["159"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js", ["160", "161"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js", ["162", "163", "164", "165", "166", "167", "168", "169", "170", "171", "172"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 11, "column": 8, "nodeType": "175", "messageId": "176", "endLine": 11, "endColumn": 22}, {"ruleId": "173", "severity": 1, "message": "177", "line": 38, "column": 11, "nodeType": "175", "messageId": "176", "endLine": 38, "endColumn": 15}, {"ruleId": "173", "severity": 1, "message": "178", "line": 13, "column": 29, "nodeType": "175", "messageId": "176", "endLine": 13, "endColumn": 34}, {"ruleId": "179", "severity": 1, "message": "180", "line": 150, "column": 43, "nodeType": "181", "messageId": "182", "endLine": 150, "endColumn": 44, "suggestions": "183"}, {"ruleId": "179", "severity": 1, "message": "184", "line": 150, "column": 45, "nodeType": "181", "messageId": "182", "endLine": 150, "endColumn": 46, "suggestions": "185"}, {"ruleId": "186", "severity": 1, "message": "187", "line": 23, "column": 15, "nodeType": "188", "endLine": 23, "endColumn": 92}, {"ruleId": "186", "severity": 1, "message": "187", "line": 26, "column": 15, "nodeType": "188", "endLine": 26, "endColumn": 92}, {"ruleId": "186", "severity": 1, "message": "187", "line": 29, "column": 15, "nodeType": "188", "endLine": 29, "endColumn": 92}, {"ruleId": "186", "severity": 1, "message": "187", "line": 72, "column": 17, "nodeType": "188", "endLine": 72, "endColumn": 102}, {"ruleId": "186", "severity": 1, "message": "187", "line": 77, "column": 17, "nodeType": "188", "endLine": 77, "endColumn": 102}, {"ruleId": "186", "severity": 1, "message": "187", "line": 82, "column": 17, "nodeType": "188", "endLine": 82, "endColumn": 102}, {"ruleId": "186", "severity": 1, "message": "187", "line": 87, "column": 17, "nodeType": "188", "endLine": 87, "endColumn": 102}, {"ruleId": "186", "severity": 1, "message": "187", "line": 92, "column": 17, "nodeType": "188", "endLine": 92, "endColumn": 102}, {"ruleId": "186", "severity": 1, "message": "187", "line": 145, "column": 15, "nodeType": "188", "endLine": 145, "endColumn": 100}, {"ruleId": "186", "severity": 1, "message": "187", "line": 148, "column": 15, "nodeType": "188", "endLine": 148, "endColumn": 100}, {"ruleId": "186", "severity": 1, "message": "187", "line": 151, "column": 15, "nodeType": "188", "endLine": 151, "endColumn": 100}, "no-unused-vars", "'LoadingSpinner' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'error' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["189", "190"], "Unnecessary escape character: \\).", ["191", "192"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"messageId": "193", "fix": "194", "desc": "195"}, {"messageId": "196", "fix": "197", "desc": "198"}, {"messageId": "193", "fix": "199", "desc": "195"}, {"messageId": "196", "fix": "200", "desc": "198"}, "removeEscape", {"range": "201", "text": "202"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "203", "text": "204"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "205", "text": "202"}, {"range": "206", "text": "204"}, [5576, 5577], "", [5576, 5576], "\\", [5578, 5579], [5578, 5578]]