[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\ErrorBoundary.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\paymentService.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\admin\\AdminDashboard.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\admin\\AdminProducts.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Debug.js": "37"}, {"size": 893, "mtime": 1753959821369, "results": "38", "hashOfConfig": "39"}, {"size": 4317, "mtime": 1754040820842, "results": "40", "hashOfConfig": "39"}, {"size": 649, "mtime": 1753959858771, "results": "41", "hashOfConfig": "39"}, {"size": 10205, "mtime": 1754040874944, "results": "42", "hashOfConfig": "39"}, {"size": 18027, "mtime": 1754031331451, "results": "43", "hashOfConfig": "39"}, {"size": 16029, "mtime": 1754029372102, "results": "44", "hashOfConfig": "39"}, {"size": 21158, "mtime": 1754039631208, "results": "45", "hashOfConfig": "39"}, {"size": 11245, "mtime": 1754032561073, "results": "46", "hashOfConfig": "39"}, {"size": 665, "mtime": 1753960412289, "results": "47", "hashOfConfig": "39"}, {"size": 512, "mtime": 1753960404241, "results": "48", "hashOfConfig": "39"}, {"size": 527, "mtime": 1753960421333, "results": "49", "hashOfConfig": "39"}, {"size": 1195, "mtime": 1753960448070, "results": "50", "hashOfConfig": "39"}, {"size": 10860, "mtime": 1753960292711, "results": "51", "hashOfConfig": "39"}, {"size": 8411, "mtime": 1753960255797, "results": "52", "hashOfConfig": "39"}, {"size": 550, "mtime": 1753960427641, "results": "53", "hashOfConfig": "39"}, {"size": 5487, "mtime": 1753959881053, "results": "54", "hashOfConfig": "39"}, {"size": 690, "mtime": 1753960437755, "results": "55", "hashOfConfig": "39"}, {"size": 6556, "mtime": 1754040908029, "results": "56", "hashOfConfig": "39"}, {"size": 2169, "mtime": 1753960017350, "results": "57", "hashOfConfig": "39"}, {"size": 7835, "mtime": 1754032577788, "results": "58", "hashOfConfig": "39"}, {"size": 4356, "mtime": 1753960002763, "results": "59", "hashOfConfig": "39"}, {"size": 11244, "mtime": 1754039837975, "results": "60", "hashOfConfig": "39"}, {"size": 533, "mtime": 1754039781263, "results": "61", "hashOfConfig": "39"}, {"size": 6787, "mtime": 1753961843906, "results": "62", "hashOfConfig": "39"}, {"size": 681, "mtime": 1753960177430, "results": "63", "hashOfConfig": "39"}, {"size": 2367, "mtime": 1753960036985, "results": "64", "hashOfConfig": "39"}, {"size": 1801, "mtime": 1754040923093, "results": "65", "hashOfConfig": "39"}, {"size": 675, "mtime": 1753960072253, "results": "66", "hashOfConfig": "39"}, {"size": 1378, "mtime": 1754032591423, "results": "67", "hashOfConfig": "39"}, {"size": 1313, "mtime": 1753960066278, "results": "68", "hashOfConfig": "39"}, {"size": 931, "mtime": 1753960025458, "results": "69", "hashOfConfig": "39"}, {"size": 1785, "mtime": 1754029155865, "results": "70", "hashOfConfig": "39"}, {"size": 2770, "mtime": 1754032884749, "results": "71", "hashOfConfig": "39"}, {"size": 1407, "mtime": 1753960081263, "results": "72", "hashOfConfig": "39"}, {"size": 10561, "mtime": 1754039688182, "results": "73", "hashOfConfig": "39"}, {"size": 14149, "mtime": 1754039734903, "results": "74", "hashOfConfig": "39"}, {"size": 5698, "mtime": 1754040777056, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c2b6v7", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js", ["187", "188"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js", ["189"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js", ["190", "191"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js", ["192"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js", ["193", "194"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js", ["195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\ErrorBoundary.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\paymentService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["206"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\admin\\AdminProducts.js", ["207", "208", "209", "210"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Debug.js", [], [], {"ruleId": "211", "severity": 1, "message": "212", "line": 11, "column": 8, "nodeType": "213", "messageId": "214", "endLine": 11, "endColumn": 22}, {"ruleId": "211", "severity": 1, "message": "215", "line": 42, "column": 11, "nodeType": "213", "messageId": "214", "endLine": 42, "endColumn": 15}, {"ruleId": "211", "severity": 1, "message": "216", "line": 7, "column": 23, "nodeType": "213", "messageId": "214", "endLine": 7, "endColumn": 50}, {"ruleId": "211", "severity": 1, "message": "217", "line": 4, "column": 10, "nodeType": "213", "messageId": "214", "endLine": 4, "endColumn": 18}, {"ruleId": "211", "severity": 1, "message": "218", "line": 5, "column": 34, "nodeType": "213", "messageId": "214", "endLine": 5, "endColumn": 40}, {"ruleId": "211", "severity": 1, "message": "219", "line": 14, "column": 29, "nodeType": "213", "messageId": "214", "endLine": 14, "endColumn": 34}, {"ruleId": "220", "severity": 1, "message": "221", "line": 150, "column": 43, "nodeType": "222", "messageId": "223", "endLine": 150, "endColumn": 44, "suggestions": "224"}, {"ruleId": "220", "severity": 1, "message": "225", "line": 150, "column": 45, "nodeType": "222", "messageId": "223", "endLine": 150, "endColumn": 46, "suggestions": "226"}, {"ruleId": "227", "severity": 1, "message": "228", "line": 23, "column": 15, "nodeType": "229", "endLine": 23, "endColumn": 92}, {"ruleId": "227", "severity": 1, "message": "228", "line": 26, "column": 15, "nodeType": "229", "endLine": 26, "endColumn": 92}, {"ruleId": "227", "severity": 1, "message": "228", "line": 29, "column": 15, "nodeType": "229", "endLine": 29, "endColumn": 92}, {"ruleId": "227", "severity": 1, "message": "228", "line": 72, "column": 17, "nodeType": "229", "endLine": 72, "endColumn": 102}, {"ruleId": "227", "severity": 1, "message": "228", "line": 77, "column": 17, "nodeType": "229", "endLine": 77, "endColumn": 102}, {"ruleId": "227", "severity": 1, "message": "228", "line": 82, "column": 17, "nodeType": "229", "endLine": 82, "endColumn": 102}, {"ruleId": "227", "severity": 1, "message": "228", "line": 87, "column": 17, "nodeType": "229", "endLine": 87, "endColumn": 102}, {"ruleId": "227", "severity": 1, "message": "228", "line": 92, "column": 17, "nodeType": "229", "endLine": 92, "endColumn": 102}, {"ruleId": "227", "severity": 1, "message": "228", "line": 145, "column": 15, "nodeType": "229", "endLine": 145, "endColumn": 100}, {"ruleId": "227", "severity": 1, "message": "228", "line": 148, "column": 15, "nodeType": "229", "endLine": 148, "endColumn": 100}, {"ruleId": "227", "severity": 1, "message": "228", "line": 151, "column": 15, "nodeType": "229", "endLine": 151, "endColumn": 100}, {"ruleId": "230", "severity": 1, "message": "231", "line": 41, "column": 6, "nodeType": "232", "endLine": 41, "endColumn": 29, "suggestions": "233"}, {"ruleId": "211", "severity": 1, "message": "234", "line": 6, "column": 3, "nodeType": "213", "messageId": "214", "endLine": 6, "endColumn": 11}, {"ruleId": "211", "severity": 1, "message": "235", "line": 29, "column": 10, "nodeType": "213", "messageId": "214", "endLine": 29, "endColumn": 21}, {"ruleId": "211", "severity": 1, "message": "236", "line": 29, "column": 23, "nodeType": "213", "messageId": "214", "endLine": 29, "endColumn": 37}, {"ruleId": "230", "severity": 1, "message": "237", "line": 38, "column": 6, "nodeType": "232", "endLine": 38, "endColumn": 66, "suggestions": "238"}, "no-unused-vars", "'LoadingSpinner' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'calculateDiscountPercentage' is defined but never used.", "'Elements' is defined but never used.", "'FiUser' is defined but never used.", "'error' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["239", "240"], "Unnecessary escape character: \\).", ["241", "242"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["243"], "'FiFilter' is defined but never used.", "'showFilters' is assigned a value but never used.", "'setShowFilters' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["244"], {"messageId": "245", "fix": "246", "desc": "247"}, {"messageId": "248", "fix": "249", "desc": "250"}, {"messageId": "245", "fix": "251", "desc": "247"}, {"messageId": "248", "fix": "252", "desc": "250"}, {"desc": "253", "fix": "254"}, {"desc": "255", "fix": "256"}, "removeEscape", {"range": "257", "text": "258"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "259", "text": "260"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "261", "text": "258"}, {"range": "262", "text": "260"}, "Update the dependencies array to be: [user, navigate, token, fetchDashboardData]", {"range": "263", "text": "264"}, "Update the dependencies array to be: [user, navigate, currentPage, searchQuery, selectedCategory, fetchProducts]", {"range": "265", "text": "266"}, [5576, 5577], "", [5576, 5576], "\\", [5578, 5579], [5578, 5578], [1060, 1083], "[user, navigate, token, fetchDashboardData]", [1114, 1174], "[user, navigate, currentPage, searchQuery, selectedCategory, fetchProducts]"]