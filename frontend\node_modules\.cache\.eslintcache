[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js": "32"}, {"size": 893, "mtime": 1753959821369, "results": "33", "hashOfConfig": "34"}, {"size": 3390, "mtime": 1753960096440, "results": "35", "hashOfConfig": "34"}, {"size": 649, "mtime": 1753959858771, "results": "36", "hashOfConfig": "34"}, {"size": 9892, "mtime": 1754029263531, "results": "37", "hashOfConfig": "34"}, {"size": 14431, "mtime": 1754029298958, "results": "38", "hashOfConfig": "34"}, {"size": 16029, "mtime": 1754029372102, "results": "39", "hashOfConfig": "34"}, {"size": 520, "mtime": 1753960398266, "results": "40", "hashOfConfig": "34"}, {"size": 11245, "mtime": 1754029418642, "results": "41", "hashOfConfig": "34"}, {"size": 665, "mtime": 1753960412289, "results": "42", "hashOfConfig": "34"}, {"size": 512, "mtime": 1753960404241, "results": "43", "hashOfConfig": "34"}, {"size": 527, "mtime": 1753960421333, "results": "44", "hashOfConfig": "34"}, {"size": 1195, "mtime": 1753960448070, "results": "45", "hashOfConfig": "34"}, {"size": 10860, "mtime": 1753960292711, "results": "46", "hashOfConfig": "34"}, {"size": 8411, "mtime": 1753960255797, "results": "47", "hashOfConfig": "34"}, {"size": 550, "mtime": 1753960427641, "results": "48", "hashOfConfig": "34"}, {"size": 5487, "mtime": 1753959881053, "results": "49", "hashOfConfig": "34"}, {"size": 690, "mtime": 1753960437755, "results": "50", "hashOfConfig": "34"}, {"size": 6556, "mtime": 1753959951852, "results": "51", "hashOfConfig": "34"}, {"size": 2169, "mtime": 1753960017350, "results": "52", "hashOfConfig": "34"}, {"size": 8062, "mtime": 1753959983263, "results": "53", "hashOfConfig": "34"}, {"size": 4356, "mtime": 1753960002763, "results": "54", "hashOfConfig": "34"}, {"size": 10277, "mtime": 1753961863361, "results": "55", "hashOfConfig": "34"}, {"size": 425, "mtime": 1754026881172, "results": "56", "hashOfConfig": "34"}, {"size": 6787, "mtime": 1753961843906, "results": "57", "hashOfConfig": "34"}, {"size": 681, "mtime": 1753960177430, "results": "58", "hashOfConfig": "34"}, {"size": 2367, "mtime": 1753960036985, "results": "59", "hashOfConfig": "34"}, {"size": 1801, "mtime": 1753960046714, "results": "60", "hashOfConfig": "34"}, {"size": 675, "mtime": 1753960072253, "results": "61", "hashOfConfig": "34"}, {"size": 2041, "mtime": 1753960057366, "results": "62", "hashOfConfig": "34"}, {"size": 1313, "mtime": 1753960066278, "results": "63", "hashOfConfig": "34"}, {"size": 931, "mtime": 1753960025458, "results": "64", "hashOfConfig": "34"}, {"size": 1785, "mtime": 1754029155865, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "c2b6v7", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\App.js", ["162", "163"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Home.js", ["164"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Products.js", ["165"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\ProductDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Checkout.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Cart.js", ["166"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\OrderDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Orders.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\NotFound.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Register.js", ["167", "168"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ForgotPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\authSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\pages\\auth\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\productSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\categorySlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\cartSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\store\\slices\\orderSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Navbar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\auth\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\layout\\Footer.js", ["169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\productService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\categoryService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\cartService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\orderService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\PROJECT\\Groceries store applicaion\\frontend\\src\\utils\\currency.js", [], [], {"ruleId": "180", "severity": 1, "message": "181", "line": 11, "column": 8, "nodeType": "182", "messageId": "183", "endLine": 11, "endColumn": 22}, {"ruleId": "180", "severity": 1, "message": "184", "line": 38, "column": 11, "nodeType": "182", "messageId": "183", "endLine": 38, "endColumn": 15}, {"ruleId": "180", "severity": 1, "message": "185", "line": 6, "column": 23, "nodeType": "182", "messageId": "183", "endLine": 6, "endColumn": 50}, {"ruleId": "180", "severity": 1, "message": "185", "line": 7, "column": 23, "nodeType": "182", "messageId": "183", "endLine": 7, "endColumn": 50}, {"ruleId": "180", "severity": 1, "message": "186", "line": 14, "column": 29, "nodeType": "182", "messageId": "183", "endLine": 14, "endColumn": 34}, {"ruleId": "187", "severity": 1, "message": "188", "line": 150, "column": 43, "nodeType": "189", "messageId": "190", "endLine": 150, "endColumn": 44, "suggestions": "191"}, {"ruleId": "187", "severity": 1, "message": "192", "line": 150, "column": 45, "nodeType": "189", "messageId": "190", "endLine": 150, "endColumn": 46, "suggestions": "193"}, {"ruleId": "194", "severity": 1, "message": "195", "line": 23, "column": 15, "nodeType": "196", "endLine": 23, "endColumn": 92}, {"ruleId": "194", "severity": 1, "message": "195", "line": 26, "column": 15, "nodeType": "196", "endLine": 26, "endColumn": 92}, {"ruleId": "194", "severity": 1, "message": "195", "line": 29, "column": 15, "nodeType": "196", "endLine": 29, "endColumn": 92}, {"ruleId": "194", "severity": 1, "message": "195", "line": 72, "column": 17, "nodeType": "196", "endLine": 72, "endColumn": 102}, {"ruleId": "194", "severity": 1, "message": "195", "line": 77, "column": 17, "nodeType": "196", "endLine": 77, "endColumn": 102}, {"ruleId": "194", "severity": 1, "message": "195", "line": 82, "column": 17, "nodeType": "196", "endLine": 82, "endColumn": 102}, {"ruleId": "194", "severity": 1, "message": "195", "line": 87, "column": 17, "nodeType": "196", "endLine": 87, "endColumn": 102}, {"ruleId": "194", "severity": 1, "message": "195", "line": 92, "column": 17, "nodeType": "196", "endLine": 92, "endColumn": 102}, {"ruleId": "194", "severity": 1, "message": "195", "line": 145, "column": 15, "nodeType": "196", "endLine": 145, "endColumn": 100}, {"ruleId": "194", "severity": 1, "message": "195", "line": 148, "column": 15, "nodeType": "196", "endLine": 148, "endColumn": 100}, {"ruleId": "194", "severity": 1, "message": "195", "line": 151, "column": 15, "nodeType": "196", "endLine": 151, "endColumn": 100}, "no-unused-vars", "'LoadingSpinner' is defined but never used.", "Identifier", "unusedVar", "'user' is assigned a value but never used.", "'calculateDiscountPercentage' is defined but never used.", "'error' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\(.", "Literal", "unnecessaryEscape", ["197", "198"], "Unnecessary escape character: \\).", ["199", "200"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", {"messageId": "201", "fix": "202", "desc": "203"}, {"messageId": "204", "fix": "205", "desc": "206"}, {"messageId": "201", "fix": "207", "desc": "203"}, {"messageId": "204", "fix": "208", "desc": "206"}, "removeEscape", {"range": "209", "text": "210"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "211", "text": "212"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "213", "text": "210"}, {"range": "214", "text": "212"}, [5576, 5577], "", [5576, 5576], "\\", [5578, 5579], [5578, 5578]]