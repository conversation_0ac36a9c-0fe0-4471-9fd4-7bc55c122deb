{"name": "bahuchar-groceries-backend", "version": "1.0.0", "description": "Backend API for Bahuchar Groceries Store", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedProducts.js", "create-admin": "node scripts/createAdmin.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "express-validator": "^7.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "stripe": "^14.9.0", "nodemailer": "^6.9.7", "cloudinary": "^1.41.0", "express-fileupload": "^1.4.3", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "api", "grocery", "ecommerce", "b<PERSON><PERSON><PERSON>"], "author": "Bahuchar Groceries Store", "license": "MIT"}