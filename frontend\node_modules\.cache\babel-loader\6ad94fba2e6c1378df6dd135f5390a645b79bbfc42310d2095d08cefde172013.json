{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\NotFound.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiHome } from 'react-icons/fi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst NotFound = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sm:mx-auto sm:w-full sm:max-w-md\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-6xl font-bold text-green-600 mb-4\",\n            children: \"404\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-gray-900 mb-2\",\n            children: \"Page Not Found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-6\",\n            children: \"Sorry, the page you are looking for doesn't exist or has been moved.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n          children: [/*#__PURE__*/_jsxDEV(FiHome, {\n            className: \"w-4 h-4 mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), \"Go Home\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 7,\n    columnNumber: 5\n  }, this);\n};\n_c = NotFound;\nexport default NotFound;\nvar _c;\n$RefreshReg$(_c, \"NotFound\");", "map": {"version": 3, "names": ["React", "Link", "FiHome", "jsxDEV", "_jsxDEV", "NotFound", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/NotFound.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { FiHome } from 'react-icons/fi';\n\nconst NotFound = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10 text-center\">\n          <div className=\"mb-6\">\n            <h1 className=\"text-6xl font-bold text-green-600 mb-4\">404</h1>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Page Not Found</h2>\n            <p className=\"text-gray-600 mb-6\">\n              Sorry, the page you are looking for doesn't exist or has been moved.\n            </p>\n          </div>\n          \n          <Link\n            to=\"/\"\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n          >\n            <FiHome className=\"w-4 h-4 mr-2\" />\n            Go Home\n          </Link>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFound;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EACrB,oBACED,OAAA;IAAKE,SAAS,EAAC,4EAA4E;IAAAC,QAAA,eACzFH,OAAA;MAAKE,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CH,OAAA;QAAKE,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EH,OAAA;UAAKE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBH,OAAA;YAAIE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DP,OAAA;YAAIE,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEP,OAAA;YAAGE,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAElC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENP,OAAA,CAACH,IAAI;UACHW,EAAE,EAAC,GAAG;UACNN,SAAS,EAAC,iNAAiN;UAAAC,QAAA,gBAE3NH,OAAA,CAACF,MAAM;YAACI,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,WAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GAxBIR,QAAQ;AA0Bd,eAAeA,QAAQ;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}