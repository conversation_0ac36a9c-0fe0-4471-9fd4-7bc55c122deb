const mongoose = require('mongoose');
const Product = require('../models/Product');
const Category = require('../models/Category');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grocery-store')
  .then(() => console.log('✅ MongoDB connected for seeding'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

// Categories data
const categories = [
  { name: 'Fresh Fruits', slug: 'fresh-fruits', description: 'Fresh seasonal fruits', image: { url: '/images/categories/fruits.jpg' } },
  { name: 'Fresh Vegetables', slug: 'fresh-vegetables', description: 'Farm fresh vegetables', image: { url: '/images/categories/vegetables.jpg' } },
  { name: 'Dairy & Eggs', slug: 'dairy-eggs', description: 'Milk, cheese, yogurt and eggs', image: { url: '/images/categories/dairy.jpg' } },
  { name: 'Meat & Seafood', slug: 'meat-seafood', description: 'Fresh meat and seafood', image: { url: '/images/categories/meat.jpg' } },
  { name: 'Bakery & Bread', slug: 'bakery-bread', description: 'Fresh baked goods', image: { url: '/images/categories/bakery.jpg' } },
  { name: 'Pantry Staples', slug: 'pantry-staples', description: 'Rice, flour, oil and spices', image: { url: '/images/categories/pantry.jpg' } },
  { name: 'Snacks & Sweets', slug: 'snacks-sweets', description: 'Chips, cookies and candies', image: { url: '/images/categories/snacks.jpg' } },
  { name: 'Beverages', slug: 'beverages', description: 'Juices, soft drinks and water', image: { url: '/images/categories/beverages.jpg' } },
  { name: 'Frozen Foods', slug: 'frozen-foods', description: 'Frozen vegetables and ready meals', image: { url: '/images/categories/frozen.jpg' } },
  { name: 'Health & Wellness', slug: 'health-wellness', description: 'Organic and health products', image: { url: '/images/categories/health.jpg' } },
  { name: 'Baby Care', slug: 'baby-care', description: 'Baby food and care products', image: { url: '/images/categories/baby.jpg' } },
  { name: 'Household Items', slug: 'household-items', description: 'Cleaning and household supplies', image: { url: '/images/categories/household.jpg' } }
];

// Products data - Over 200 items
const products = [
  // Fresh Fruits (25 items)
  { name: 'Fresh Bananas', price: 2.99, originalPrice: 3.49, category: 'Fresh Fruits', description: 'Sweet ripe bananas, perfect for snacking', stock: 150, weight: '1 lb', brand: 'Local Farm', images: ['/images/products/bananas.jpg'] },
  { name: 'Red Apples', price: 4.99, originalPrice: 5.99, category: 'Fresh Fruits', description: 'Crisp and juicy red apples', stock: 120, weight: '2 lbs', brand: 'Orchard Fresh', images: ['/images/products/red-apples.jpg'] },
  { name: 'Green Apples', price: 4.99, category: 'Fresh Fruits', description: 'Tart and crispy green apples', stock: 100, weight: '2 lbs', brand: 'Orchard Fresh', images: ['/images/products/green-apples.jpg'] },
  { name: 'Fresh Oranges', price: 5.99, category: 'Fresh Fruits', description: 'Juicy Valencia oranges', stock: 80, weight: '3 lbs', brand: 'Citrus Grove', images: ['/images/products/oranges.jpg'] },
  { name: 'Strawberries', price: 6.99, originalPrice: 7.99, category: 'Fresh Fruits', description: 'Sweet fresh strawberries', stock: 60, weight: '1 lb', brand: 'Berry Farm', images: ['/images/products/strawberries.jpg'] },
  { name: 'Blueberries', price: 8.99, category: 'Fresh Fruits', description: 'Antioxidant-rich blueberries', stock: 45, weight: '1 lb', brand: 'Berry Farm', images: ['/images/products/blueberries.jpg'] },
  { name: 'Fresh Grapes', price: 7.99, category: 'Fresh Fruits', description: 'Sweet seedless grapes', stock: 70, weight: '2 lbs', brand: 'Vineyard Select', images: ['/images/products/grapes.jpg'] },
  { name: 'Pineapple', price: 4.99, category: 'Fresh Fruits', description: 'Sweet tropical pineapple', stock: 30, weight: '1 whole', brand: 'Tropical Farms', images: ['/images/products/pineapple.jpg'] },
  { name: 'Mangoes', price: 3.99, category: 'Fresh Fruits', description: 'Ripe and sweet mangoes', stock: 50, weight: '2 pieces', brand: 'Tropical Farms', images: ['/images/products/mangoes.jpg'] },
  { name: 'Avocados', price: 5.99, category: 'Fresh Fruits', description: 'Creamy Hass avocados', stock: 90, weight: '4 pieces', brand: 'California Fresh', images: ['/images/products/avocados.jpg'] },
  { name: 'Lemons', price: 3.99, category: 'Fresh Fruits', description: 'Fresh juicy lemons', stock: 100, weight: '2 lbs', brand: 'Citrus Grove', images: ['/images/products/lemons.jpg'] },
  { name: 'Limes', price: 3.99, category: 'Fresh Fruits', description: 'Tangy fresh limes', stock: 80, weight: '1 lb', brand: 'Citrus Grove', images: ['/images/products/limes.jpg'] },
  { name: 'Watermelon', price: 8.99, category: 'Fresh Fruits', description: 'Sweet juicy watermelon', stock: 20, weight: '1 whole', brand: 'Summer Farms', images: ['/images/products/watermelon.jpg'] },
  { name: 'Cantaloupe', price: 4.99, category: 'Fresh Fruits', description: 'Sweet orange cantaloupe', stock: 25, weight: '1 whole', brand: 'Summer Farms', images: ['/images/products/cantaloupe.jpg'] },
  { name: 'Honeydew Melon', price: 5.99, category: 'Fresh Fruits', description: 'Sweet green honeydew', stock: 20, weight: '1 whole', brand: 'Summer Farms', images: ['/images/products/honeydew.jpg'] },
  { name: 'Peaches', price: 6.99, category: 'Fresh Fruits', description: 'Juicy summer peaches', stock: 40, weight: '2 lbs', brand: 'Stone Fruit Co', images: ['/images/products/peaches.jpg'] },
  { name: 'Plums', price: 5.99, category: 'Fresh Fruits', description: 'Sweet purple plums', stock: 35, weight: '2 lbs', brand: 'Stone Fruit Co', images: ['/images/products/plums.jpg'] },
  { name: 'Cherries', price: 12.99, originalPrice: 14.99, category: 'Fresh Fruits', description: 'Sweet red cherries', stock: 25, weight: '1 lb', brand: 'Cherry Hill', images: ['/images/products/cherries.jpg'] },
  { name: 'Kiwi Fruit', price: 4.99, category: 'Fresh Fruits', description: 'Tangy kiwi fruit', stock: 60, weight: '6 pieces', brand: 'Exotic Fruits', images: ['/images/products/kiwi.jpg'] },
  { name: 'Papaya', price: 6.99, category: 'Fresh Fruits', description: 'Sweet tropical papaya', stock: 15, weight: '1 whole', brand: 'Tropical Farms', images: ['/images/products/papaya.jpg'] },
  { name: 'Pomegranate', price: 7.99, category: 'Fresh Fruits', description: 'Antioxidant-rich pomegranate', stock: 30, weight: '2 pieces', brand: 'Exotic Fruits', images: ['/images/products/pomegranate.jpg'] },
  { name: 'Dragon Fruit', price: 9.99, category: 'Fresh Fruits', description: 'Exotic dragon fruit', stock: 20, weight: '2 pieces', brand: 'Exotic Fruits', images: ['/images/products/dragon-fruit.jpg'] },
  { name: 'Coconut', price: 3.99, category: 'Fresh Fruits', description: 'Fresh coconut', stock: 25, weight: '1 whole', brand: 'Tropical Farms', images: ['/images/products/coconut.jpg'] },
  { name: 'Passion Fruit', price: 8.99, category: 'Fresh Fruits', description: 'Aromatic passion fruit', stock: 40, weight: '6 pieces', brand: 'Exotic Fruits', images: ['/images/products/passion-fruit.jpg'] },
  { name: 'Star Fruit', price: 6.99, category: 'Fresh Fruits', description: 'Unique star-shaped fruit', stock: 30, weight: '4 pieces', brand: 'Exotic Fruits', images: ['/images/products/star-fruit.jpg'] },

  // Fresh Vegetables (30 items)
  { name: 'Fresh Tomatoes', price: 3.99, category: 'Fresh Vegetables', description: 'Vine-ripened tomatoes', stock: 100, weight: '2 lbs', brand: 'Garden Fresh', images: ['/images/products/tomatoes.jpg'] },
  { name: 'Cucumbers', price: 2.99, category: 'Fresh Vegetables', description: 'Crisp fresh cucumbers', stock: 80, weight: '3 pieces', brand: 'Garden Fresh', images: ['/images/products/cucumbers.jpg'] },
  { name: 'Bell Peppers', price: 4.99, category: 'Fresh Vegetables', description: 'Colorful bell peppers', stock: 70, weight: '3 pieces', brand: 'Garden Fresh', images: ['/images/products/bell-peppers.jpg'] },
  { name: 'Onions', price: 2.49, category: 'Fresh Vegetables', description: 'Yellow cooking onions', stock: 120, weight: '3 lbs', brand: 'Farm Select', images: ['/images/products/onions.jpg'] },
  { name: 'Red Onions', price: 2.99, category: 'Fresh Vegetables', description: 'Sweet red onions', stock: 90, weight: '2 lbs', brand: 'Farm Select', images: ['/images/products/red-onions.jpg'] },
  { name: 'Carrots', price: 2.99, category: 'Fresh Vegetables', description: 'Fresh orange carrots', stock: 100, weight: '2 lbs', brand: 'Root Vegetables Co', images: ['/images/products/carrots.jpg'] },
  { name: 'Celery', price: 3.49, category: 'Fresh Vegetables', description: 'Crisp celery stalks', stock: 60, weight: '1 bunch', brand: 'Garden Fresh', images: ['/images/products/celery.jpg'] },
  { name: 'Broccoli', price: 4.99, category: 'Fresh Vegetables', description: 'Fresh green broccoli', stock: 50, weight: '1 head', brand: 'Green Valley', images: ['/images/products/broccoli.jpg'] },
  { name: 'Cauliflower', price: 5.99, category: 'Fresh Vegetables', description: 'White cauliflower head', stock: 40, weight: '1 head', brand: 'Green Valley', images: ['/images/products/cauliflower.jpg'] },
  { name: 'Spinach', price: 3.99, originalPrice: 4.49, category: 'Fresh Vegetables', description: 'Fresh baby spinach', stock: 70, weight: '5 oz bag', brand: 'Leafy Greens', images: ['/images/products/spinach.jpg'] },
  { name: 'Lettuce', price: 2.99, category: 'Fresh Vegetables', description: 'Crisp iceberg lettuce', stock: 60, weight: '1 head', brand: 'Leafy Greens', images: ['/images/products/lettuce.jpg'] },
  { name: 'Kale', price: 4.99, category: 'Fresh Vegetables', description: 'Nutritious kale leaves', stock: 40, weight: '1 bunch', brand: 'Leafy Greens', images: ['/images/products/kale.jpg'] },
  { name: 'Potatoes', price: 3.99, category: 'Fresh Vegetables', description: 'Russet potatoes', stock: 150, weight: '5 lbs', brand: 'Root Vegetables Co', images: ['/images/products/potatoes.jpg'] },
  { name: 'Sweet Potatoes', price: 4.99, category: 'Fresh Vegetables', description: 'Orange sweet potatoes', stock: 80, weight: '3 lbs', brand: 'Root Vegetables Co', images: ['/images/products/sweet-potatoes.jpg'] },
  { name: 'Garlic', price: 1.99, category: 'Fresh Vegetables', description: 'Fresh garlic bulbs', stock: 100, weight: '3 bulbs', brand: 'Herb Garden', images: ['/images/products/garlic.jpg'] },
  { name: 'Ginger', price: 3.99, category: 'Fresh Vegetables', description: 'Fresh ginger root', stock: 60, weight: '4 oz', brand: 'Herb Garden', images: ['/images/products/ginger.jpg'] },
  { name: 'Green Beans', price: 4.99, category: 'Fresh Vegetables', description: 'Fresh green beans', stock: 70, weight: '1 lb', brand: 'Garden Fresh', images: ['/images/products/green-beans.jpg'] },
  { name: 'Asparagus', price: 6.99, category: 'Fresh Vegetables', description: 'Fresh asparagus spears', stock: 30, weight: '1 lb', brand: 'Garden Fresh', images: ['/images/products/asparagus.jpg'] },
  { name: 'Zucchini', price: 3.99, category: 'Fresh Vegetables', description: 'Fresh zucchini squash', stock: 50, weight: '2 pieces', brand: 'Garden Fresh', images: ['/images/products/zucchini.jpg'] },
  { name: 'Yellow Squash', price: 3.99, category: 'Fresh Vegetables', description: 'Fresh yellow squash', stock: 45, weight: '2 pieces', brand: 'Garden Fresh', images: ['/images/products/yellow-squash.jpg'] },
  { name: 'Eggplant', price: 4.99, category: 'Fresh Vegetables', description: 'Purple eggplant', stock: 35, weight: '1 piece', brand: 'Garden Fresh', images: ['/images/products/eggplant.jpg'] },
  { name: 'Mushrooms', price: 4.99, category: 'Fresh Vegetables', description: 'White button mushrooms', stock: 60, weight: '8 oz', brand: 'Fungi Farm', images: ['/images/products/mushrooms.jpg'] },
  { name: 'Baby Corn', price: 3.99, category: 'Fresh Vegetables', description: 'Fresh baby corn', stock: 40, weight: '8 oz', brand: 'Garden Fresh', images: ['/images/products/baby-corn.jpg'] },
  { name: 'Brussels Sprouts', price: 5.99, category: 'Fresh Vegetables', description: 'Fresh Brussels sprouts', stock: 30, weight: '1 lb', brand: 'Green Valley', images: ['/images/products/brussels-sprouts.jpg'] },
  { name: 'Cabbage', price: 2.99, category: 'Fresh Vegetables', description: 'Green cabbage head', stock: 50, weight: '1 head', brand: 'Green Valley', images: ['/images/products/cabbage.jpg'] },
  { name: 'Red Cabbage', price: 3.99, category: 'Fresh Vegetables', description: 'Purple red cabbage', stock: 40, weight: '1 head', brand: 'Green Valley', images: ['/images/products/red-cabbage.jpg'] },
  { name: 'Radishes', price: 2.99, category: 'Fresh Vegetables', description: 'Fresh red radishes', stock: 50, weight: '1 bunch', brand: 'Root Vegetables Co', images: ['/images/products/radishes.jpg'] },
  { name: 'Beets', price: 4.99, category: 'Fresh Vegetables', description: 'Fresh red beets', stock: 40, weight: '2 lbs', brand: 'Root Vegetables Co', images: ['/images/products/beets.jpg'] },
  { name: 'Turnips', price: 3.99, category: 'Fresh Vegetables', description: 'Fresh turnips', stock: 30, weight: '2 lbs', brand: 'Root Vegetables Co', images: ['/images/products/turnips.jpg'] },

  // Dairy & Eggs (20 items)
  { name: 'Whole Milk', price: 4.99, category: 'Dairy & Eggs', description: 'Fresh whole milk', stock: 100, weight: '1 gallon', brand: 'Dairy Fresh', images: ['/images/products/whole-milk.jpg'] },
  { name: '2% Milk', price: 4.79, category: 'Dairy & Eggs', description: 'Reduced fat milk', stock: 120, weight: '1 gallon', brand: 'Dairy Fresh', images: ['/images/products/2-percent-milk.jpg'] },
  { name: 'Skim Milk', price: 4.59, category: 'Dairy & Eggs', description: 'Fat-free skim milk', stock: 80, weight: '1 gallon', brand: 'Dairy Fresh', images: ['/images/products/skim-milk.jpg'] },
  { name: 'Almond Milk', price: 5.99, category: 'Dairy & Eggs', description: 'Unsweetened almond milk', stock: 60, weight: '64 fl oz', brand: 'Plant Based', images: ['/images/products/almond-milk.jpg'] },
  { name: 'Oat Milk', price: 6.49, category: 'Dairy & Eggs', description: 'Creamy oat milk', stock: 50, weight: '64 fl oz', brand: 'Plant Based', images: ['/images/products/oat-milk.jpg'] },
  { name: 'Greek Yogurt', price: 6.99, originalPrice: 7.99, category: 'Dairy & Eggs', description: 'Plain Greek yogurt', stock: 70, weight: '32 oz', brand: 'Greek Valley', images: ['/images/products/greek-yogurt.jpg'] },
  { name: 'Vanilla Yogurt', price: 5.99, category: 'Dairy & Eggs', description: 'Vanilla flavored yogurt', stock: 60, weight: '32 oz', brand: 'Creamy Delight', images: ['/images/products/vanilla-yogurt.jpg'] },
  { name: 'Strawberry Yogurt', price: 5.99, category: 'Dairy & Eggs', description: 'Strawberry yogurt', stock: 55, weight: '32 oz', brand: 'Creamy Delight', images: ['/images/products/strawberry-yogurt.jpg'] },
  { name: 'Cheddar Cheese', price: 7.99, category: 'Dairy & Eggs', description: 'Sharp cheddar cheese', stock: 80, weight: '8 oz', brand: 'Cheese Master', images: ['/images/products/cheddar-cheese.jpg'] },
  { name: 'Mozzarella Cheese', price: 6.99, category: 'Dairy & Eggs', description: 'Fresh mozzarella cheese', stock: 70, weight: '8 oz', brand: 'Cheese Master', images: ['/images/products/mozzarella-cheese.jpg'] },
  { name: 'Swiss Cheese', price: 8.99, category: 'Dairy & Eggs', description: 'Swiss cheese slices', stock: 50, weight: '8 oz', brand: 'Cheese Master', images: ['/images/products/swiss-cheese.jpg'] },
  { name: 'Cream Cheese', price: 3.99, category: 'Dairy & Eggs', description: 'Philadelphia cream cheese', stock: 90, weight: '8 oz', brand: 'Philadelphia', images: ['/images/products/cream-cheese.jpg'] },
  { name: 'Butter', price: 5.99, category: 'Dairy & Eggs', description: 'Unsalted butter', stock: 100, weight: '1 lb', brand: 'Dairy Fresh', images: ['/images/products/butter.jpg'] },
  { name: 'Large Eggs', price: 3.99, originalPrice: 4.49, category: 'Dairy & Eggs', description: 'Grade A large eggs', stock: 150, weight: '12 count', brand: 'Farm Fresh', images: ['/images/products/large-eggs.jpg'] },
  { name: 'Organic Eggs', price: 6.99, category: 'Dairy & Eggs', description: 'Organic free-range eggs', stock: 80, weight: '12 count', brand: 'Organic Valley', images: ['/images/products/organic-eggs.jpg'] },
  { name: 'Heavy Cream', price: 4.99, category: 'Dairy & Eggs', description: 'Heavy whipping cream', stock: 60, weight: '16 fl oz', brand: 'Dairy Fresh', images: ['/images/products/heavy-cream.jpg'] },
  { name: 'Sour Cream', price: 3.99, category: 'Dairy & Eggs', description: 'Regular sour cream', stock: 70, weight: '16 oz', brand: 'Dairy Fresh', images: ['/images/products/sour-cream.jpg'] },
  { name: 'Cottage Cheese', price: 4.99, category: 'Dairy & Eggs', description: 'Low-fat cottage cheese', stock: 50, weight: '16 oz', brand: 'Dairy Fresh', images: ['/images/products/cottage-cheese.jpg'] },
  { name: 'Parmesan Cheese', price: 9.99, category: 'Dairy & Eggs', description: 'Grated Parmesan cheese', stock: 40, weight: '8 oz', brand: 'Cheese Master', images: ['/images/products/parmesan-cheese.jpg'] },
  { name: 'Ricotta Cheese', price: 5.99, category: 'Dairy & Eggs', description: 'Whole milk ricotta', stock: 35, weight: '15 oz', brand: 'Cheese Master', images: ['/images/products/ricotta-cheese.jpg'] },

  // Meat & Seafood (15 items)
  { name: 'Chicken Breast', price: 12.99, category: 'Meat & Seafood', description: 'Boneless chicken breast', stock: 50, weight: '2 lbs', brand: 'Fresh Poultry', images: ['/images/products/chicken-breast.jpg'] },
  { name: 'Ground Beef', price: 8.99, originalPrice: 9.99, category: 'Meat & Seafood', description: '85% lean ground beef', stock: 60, weight: '1 lb', brand: 'Prime Cuts', images: ['/images/products/ground-beef.jpg'] },
  { name: 'Salmon Fillet', price: 18.99, category: 'Meat & Seafood', description: 'Fresh Atlantic salmon', stock: 30, weight: '1 lb', brand: 'Ocean Fresh', images: ['/images/products/salmon-fillet.jpg'] },
  { name: 'Shrimp', price: 15.99, category: 'Meat & Seafood', description: 'Large peeled shrimp', stock: 40, weight: '1 lb', brand: 'Ocean Fresh', images: ['/images/products/shrimp.jpg'] },
  { name: 'Pork Chops', price: 10.99, category: 'Meat & Seafood', description: 'Bone-in pork chops', stock: 35, weight: '2 lbs', brand: 'Prime Cuts', images: ['/images/products/pork-chops.jpg'] },
  { name: 'Turkey Breast', price: 14.99, category: 'Meat & Seafood', description: 'Sliced turkey breast', stock: 25, weight: '1 lb', brand: 'Deli Fresh', images: ['/images/products/turkey-breast.jpg'] },
  { name: 'Ham Slices', price: 9.99, category: 'Meat & Seafood', description: 'Honey glazed ham', stock: 30, weight: '1 lb', brand: 'Deli Fresh', images: ['/images/products/ham-slices.jpg'] },
  { name: 'Bacon', price: 7.99, category: 'Meat & Seafood', description: 'Thick cut bacon', stock: 80, weight: '1 lb', brand: 'Smokehouse', images: ['/images/products/bacon.jpg'] },
  { name: 'Sausages', price: 6.99, category: 'Meat & Seafood', description: 'Italian sausages', stock: 45, weight: '1 lb', brand: 'Butcher Shop', images: ['/images/products/sausages.jpg'] },
  { name: 'Tuna Steaks', price: 16.99, category: 'Meat & Seafood', description: 'Fresh tuna steaks', stock: 20, weight: '1 lb', brand: 'Ocean Fresh', images: ['/images/products/tuna-steaks.jpg'] },
  { name: 'Cod Fillet', price: 13.99, category: 'Meat & Seafood', description: 'Fresh cod fillet', stock: 25, weight: '1 lb', brand: 'Ocean Fresh', images: ['/images/products/cod-fillet.jpg'] },
  { name: 'Chicken Thighs', price: 8.99, category: 'Meat & Seafood', description: 'Bone-in chicken thighs', stock: 40, weight: '2 lbs', brand: 'Fresh Poultry', images: ['/images/products/chicken-thighs.jpg'] },
  { name: 'Ground Turkey', price: 9.99, category: 'Meat & Seafood', description: 'Lean ground turkey', stock: 35, weight: '1 lb', brand: 'Fresh Poultry', images: ['/images/products/ground-turkey.jpg'] },
  { name: 'Lamb Chops', price: 22.99, category: 'Meat & Seafood', description: 'Premium lamb chops', stock: 15, weight: '1 lb', brand: 'Prime Cuts', images: ['/images/products/lamb-chops.jpg'] },
  { name: 'Crab Legs', price: 24.99, category: 'Meat & Seafood', description: 'Snow crab legs', stock: 20, weight: '1 lb', brand: 'Ocean Fresh', images: ['/images/products/crab-legs.jpg'] },

  // Bakery & Bread (15 items)
  { name: 'White Bread', price: 2.99, category: 'Bakery & Bread', description: 'Soft white bread loaf', stock: 80, weight: '20 oz', brand: 'Fresh Baked', images: ['/images/products/white-bread.jpg'] },
  { name: 'Whole Wheat Bread', price: 3.99, category: 'Bakery & Bread', description: '100% whole wheat bread', stock: 70, weight: '20 oz', brand: 'Healthy Choice', images: ['/images/products/whole-wheat-bread.jpg'] },
  { name: 'Sourdough Bread', price: 4.99, category: 'Bakery & Bread', description: 'Artisan sourdough loaf', stock: 40, weight: '24 oz', brand: 'Artisan Bakery', images: ['/images/products/sourdough-bread.jpg'] },
  { name: 'Bagels', price: 5.99, originalPrice: 6.99, category: 'Bakery & Bread', description: 'Everything bagels', stock: 50, weight: '6 pack', brand: 'Morning Fresh', images: ['/images/products/bagels.jpg'] },
  { name: 'Croissants', price: 6.99, category: 'Bakery & Bread', description: 'Butter croissants', stock: 30, weight: '4 pack', brand: 'French Bakery', images: ['/images/products/croissants.jpg'] },
  { name: 'Dinner Rolls', price: 3.99, category: 'Bakery & Bread', description: 'Soft dinner rolls', stock: 60, weight: '8 pack', brand: 'Fresh Baked', images: ['/images/products/dinner-rolls.jpg'] },
  { name: 'Muffins', price: 7.99, category: 'Bakery & Bread', description: 'Blueberry muffins', stock: 40, weight: '6 pack', brand: 'Sweet Treats', images: ['/images/products/muffins.jpg'] },
  { name: 'Donuts', price: 8.99, category: 'Bakery & Bread', description: 'Glazed donuts', stock: 35, weight: '6 pack', brand: 'Sweet Treats', images: ['/images/products/donuts.jpg'] },
  { name: 'Cookies', price: 5.99, category: 'Bakery & Bread', description: 'Chocolate chip cookies', stock: 50, weight: '12 pack', brand: 'Cookie Co', images: ['/images/products/cookies.jpg'] },
  { name: 'Cake', price: 15.99, category: 'Bakery & Bread', description: 'Chocolate birthday cake', stock: 15, weight: '8 inch', brand: 'Celebration Cakes', images: ['/images/products/cake.jpg'] },
  { name: 'Pie', price: 12.99, category: 'Bakery & Bread', description: 'Apple pie', stock: 20, weight: '9 inch', brand: 'Home Style', images: ['/images/products/pie.jpg'] },
  { name: 'Danish Pastry', price: 4.99, category: 'Bakery & Bread', description: 'Cheese danish', stock: 25, weight: '2 pack', brand: 'European Bakery', images: ['/images/products/danish-pastry.jpg'] },
  { name: 'Cinnamon Rolls', price: 6.99, category: 'Bakery & Bread', description: 'Iced cinnamon rolls', stock: 30, weight: '4 pack', brand: 'Sweet Morning', images: ['/images/products/cinnamon-rolls.jpg'] },
  { name: 'Baguette', price: 3.99, category: 'Bakery & Bread', description: 'French baguette', stock: 35, weight: '1 loaf', brand: 'French Bakery', images: ['/images/products/baguette.jpg'] },
  { name: 'Pita Bread', price: 3.49, category: 'Bakery & Bread', description: 'Whole wheat pita', stock: 45, weight: '6 pack', brand: 'Mediterranean', images: ['/images/products/pita-bread.jpg'] },

  // Pantry Staples (25 items)
  { name: 'Basmati Rice', price: 8.99, category: 'Pantry Staples', description: 'Premium basmati rice', stock: 100, weight: '5 lbs', brand: 'Royal Grain', images: ['/images/products/basmati-rice.jpg'] },
  { name: 'Jasmine Rice', price: 7.99, category: 'Pantry Staples', description: 'Fragrant jasmine rice', stock: 80, weight: '5 lbs', brand: 'Asian Best', images: ['/images/products/jasmine-rice.jpg'] },
  { name: 'Brown Rice', price: 6.99, originalPrice: 7.99, category: 'Pantry Staples', description: 'Whole grain brown rice', stock: 70, weight: '5 lbs', brand: 'Healthy Grains', images: ['/images/products/brown-rice.jpg'] },
  { name: 'Quinoa', price: 12.99, category: 'Pantry Staples', description: 'Organic quinoa', stock: 50, weight: '2 lbs', brand: 'Super Grains', images: ['/images/products/quinoa.jpg'] },
  { name: 'Pasta - Spaghetti', price: 2.99, category: 'Pantry Staples', description: 'Durum wheat spaghetti', stock: 120, weight: '1 lb', brand: 'Italian Classic', images: ['/images/products/spaghetti.jpg'] },
  { name: 'Pasta - Penne', price: 2.99, category: 'Pantry Staples', description: 'Penne pasta', stock: 100, weight: '1 lb', brand: 'Italian Classic', images: ['/images/products/penne.jpg'] },
  { name: 'All-Purpose Flour', price: 4.99, category: 'Pantry Staples', description: 'Unbleached flour', stock: 80, weight: '5 lbs', brand: 'Baker\'s Choice', images: ['/images/products/flour.jpg'] },
  { name: 'Sugar', price: 3.99, category: 'Pantry Staples', description: 'Granulated white sugar', stock: 90, weight: '4 lbs', brand: 'Sweet Life', images: ['/images/products/sugar.jpg'] },
  { name: 'Brown Sugar', price: 4.49, category: 'Pantry Staples', description: 'Light brown sugar', stock: 70, weight: '2 lbs', brand: 'Sweet Life', images: ['/images/products/brown-sugar.jpg'] },
  { name: 'Olive Oil', price: 12.99, category: 'Pantry Staples', description: 'Extra virgin olive oil', stock: 60, weight: '500ml', brand: 'Mediterranean Gold', images: ['/images/products/olive-oil.jpg'] },
  { name: 'Vegetable Oil', price: 6.99, category: 'Pantry Staples', description: 'Pure vegetable oil', stock: 80, weight: '48 fl oz', brand: 'Kitchen Essential', images: ['/images/products/vegetable-oil.jpg'] },
  { name: 'Salt', price: 1.99, category: 'Pantry Staples', description: 'Iodized table salt', stock: 150, weight: '26 oz', brand: 'Pure Salt', images: ['/images/products/salt.jpg'] },
  { name: 'Black Pepper', price: 4.99, category: 'Pantry Staples', description: 'Ground black pepper', stock: 80, weight: '4 oz', brand: 'Spice Master', images: ['/images/products/black-pepper.jpg'] },
  { name: 'Turmeric Powder', price: 3.99, category: 'Pantry Staples', description: 'Pure turmeric powder', stock: 60, weight: '3 oz', brand: 'Spice Master', images: ['/images/products/turmeric.jpg'] },
  { name: 'Cumin Powder', price: 4.49, category: 'Pantry Staples', description: 'Ground cumin', stock: 50, weight: '3 oz', brand: 'Spice Master', images: ['/images/products/cumin.jpg'] },
  { name: 'Coriander Powder', price: 3.99, category: 'Pantry Staples', description: 'Ground coriander', stock: 45, weight: '3 oz', brand: 'Spice Master', images: ['/images/products/coriander.jpg'] },
  { name: 'Red Chili Powder', price: 4.99, category: 'Pantry Staples', description: 'Hot chili powder', stock: 55, weight: '4 oz', brand: 'Spice Master', images: ['/images/products/chili-powder.jpg'] },
  { name: 'Garam Masala', price: 5.99, category: 'Pantry Staples', description: 'Blend of spices', stock: 40, weight: '3 oz', brand: 'Spice Master', images: ['/images/products/garam-masala.jpg'] },
  { name: 'Baking Powder', price: 2.99, category: 'Pantry Staples', description: 'Double acting baking powder', stock: 70, weight: '10 oz', brand: 'Baker\'s Choice', images: ['/images/products/baking-powder.jpg'] },
  { name: 'Baking Soda', price: 1.99, category: 'Pantry Staples', description: 'Pure baking soda', stock: 80, weight: '16 oz', brand: 'Baker\'s Choice', images: ['/images/products/baking-soda.jpg'] },
  { name: 'Vanilla Extract', price: 6.99, category: 'Pantry Staples', description: 'Pure vanilla extract', stock: 50, weight: '4 fl oz', brand: 'Flavor Pure', images: ['/images/products/vanilla-extract.jpg'] },
  { name: 'Honey', price: 8.99, category: 'Pantry Staples', description: 'Raw wildflower honey', stock: 60, weight: '12 oz', brand: 'Nature\'s Sweet', images: ['/images/products/honey.jpg'] },
  { name: 'Maple Syrup', price: 12.99, category: 'Pantry Staples', description: 'Pure maple syrup', stock: 40, weight: '12 fl oz', brand: 'Vermont Gold', images: ['/images/products/maple-syrup.jpg'] },
  { name: 'Vinegar', price: 3.99, category: 'Pantry Staples', description: 'White distilled vinegar', stock: 70, weight: '32 fl oz', brand: 'Kitchen Essential', images: ['/images/products/vinegar.jpg'] },
  { name: 'Soy Sauce', price: 4.99, category: 'Pantry Staples', description: 'Low sodium soy sauce', stock: 80, weight: '15 fl oz', brand: 'Asian Kitchen', images: ['/images/products/soy-sauce.jpg'] },

  // Snacks & Sweets (20 items)
  { name: 'Potato Chips', price: 4.99, originalPrice: 5.99, category: 'Snacks & Sweets', description: 'Classic potato chips', stock: 100, weight: '8 oz', brand: 'Crispy Crunch', images: ['/images/products/potato-chips.jpg'] },
  { name: 'Tortilla Chips', price: 3.99, category: 'Snacks & Sweets', description: 'Restaurant style tortilla chips', stock: 80, weight: '13 oz', brand: 'Mexican Fiesta', images: ['/images/products/tortilla-chips.jpg'] },
  { name: 'Pretzels', price: 3.49, category: 'Snacks & Sweets', description: 'Salted pretzels', stock: 70, weight: '16 oz', brand: 'Twisted Treats', images: ['/images/products/pretzels.jpg'] },
  { name: 'Popcorn', price: 2.99, category: 'Snacks & Sweets', description: 'Butter popcorn', stock: 90, weight: '3 pack', brand: 'Movie Night', images: ['/images/products/popcorn.jpg'] },
  { name: 'Mixed Nuts', price: 12.99, category: 'Snacks & Sweets', description: 'Roasted mixed nuts', stock: 50, weight: '16 oz', brand: 'Nutty Delights', images: ['/images/products/mixed-nuts.jpg'] },
  { name: 'Almonds', price: 9.99, category: 'Snacks & Sweets', description: 'Raw almonds', stock: 60, weight: '16 oz', brand: 'Nutty Delights', images: ['/images/products/almonds.jpg'] },
  { name: 'Cashews', price: 11.99, category: 'Snacks & Sweets', description: 'Roasted cashews', stock: 45, weight: '12 oz', brand: 'Nutty Delights', images: ['/images/products/cashews.jpg'] },
  { name: 'Peanuts', price: 4.99, category: 'Snacks & Sweets', description: 'Salted peanuts', stock: 80, weight: '16 oz', brand: 'Nutty Delights', images: ['/images/products/peanuts.jpg'] },
  { name: 'Chocolate Bar', price: 2.99, category: 'Snacks & Sweets', description: 'Milk chocolate bar', stock: 120, weight: '3.5 oz', brand: 'Sweet Dreams', images: ['/images/products/chocolate-bar.jpg'] },
  { name: 'Dark Chocolate', price: 3.99, category: 'Snacks & Sweets', description: '70% dark chocolate', stock: 80, weight: '3.5 oz', brand: 'Premium Cocoa', images: ['/images/products/dark-chocolate.jpg'] },
  { name: 'Gummy Bears', price: 3.49, category: 'Snacks & Sweets', description: 'Fruit gummy bears', stock: 90, weight: '12 oz', brand: 'Chewy Treats', images: ['/images/products/gummy-bears.jpg'] },
  { name: 'Lollipops', price: 4.99, category: 'Snacks & Sweets', description: 'Assorted lollipops', stock: 70, weight: '20 pack', brand: 'Sweet Sticks', images: ['/images/products/lollipops.jpg'] },
  { name: 'Granola Bars', price: 6.99, category: 'Snacks & Sweets', description: 'Oats & honey granola bars', stock: 60, weight: '12 pack', brand: 'Healthy Snack', images: ['/images/products/granola-bars.jpg'] },
  { name: 'Crackers', price: 3.99, category: 'Snacks & Sweets', description: 'Saltine crackers', stock: 80, weight: '16 oz', brand: 'Crispy Bites', images: ['/images/products/crackers.jpg'] },
  { name: 'Cheese Crackers', price: 4.49, category: 'Snacks & Sweets', description: 'Cheddar cheese crackers', stock: 70, weight: '12 oz', brand: 'Cheesy Bites', images: ['/images/products/cheese-crackers.jpg'] },
  { name: 'Trail Mix', price: 8.99, category: 'Snacks & Sweets', description: 'Nuts, seeds & dried fruit', stock: 50, weight: '14 oz', brand: 'Adventure Mix', images: ['/images/products/trail-mix.jpg'] },
  { name: 'Dried Fruit', price: 7.99, category: 'Snacks & Sweets', description: 'Mixed dried fruits', stock: 40, weight: '12 oz', brand: 'Nature\'s Candy', images: ['/images/products/dried-fruit.jpg'] },
  { name: 'Rice Cakes', price: 3.99, category: 'Snacks & Sweets', description: 'Plain rice cakes', stock: 60, weight: '4.9 oz', brand: 'Light Bites', images: ['/images/products/rice-cakes.jpg'] },
  { name: 'Protein Bars', price: 12.99, category: 'Snacks & Sweets', description: 'Chocolate protein bars', stock: 40, weight: '12 pack', brand: 'Muscle Fuel', images: ['/images/products/protein-bars.jpg'] },
  { name: 'Energy Bars', price: 9.99, category: 'Snacks & Sweets', description: 'Natural energy bars', stock: 50, weight: '6 pack', brand: 'Power Up', images: ['/images/products/energy-bars.jpg'] },

  // Beverages (25 items)
  { name: 'Bottled Water', price: 4.99, category: 'Beverages', description: 'Pure spring water', stock: 200, weight: '24 pack', brand: 'Crystal Springs', images: ['/images/products/bottled-water.jpg'] },
  { name: 'Sparkling Water', price: 5.99, category: 'Beverages', description: 'Natural sparkling water', stock: 100, weight: '12 pack', brand: 'Bubble Fresh', images: ['/images/products/sparkling-water.jpg'] },
  { name: 'Orange Juice', price: 6.99, originalPrice: 7.99, category: 'Beverages', description: 'Fresh squeezed orange juice', stock: 80, weight: '64 fl oz', brand: 'Citrus Fresh', images: ['/images/products/orange-juice.jpg'] },
  { name: 'Apple Juice', price: 5.99, category: 'Beverages', description: '100% apple juice', stock: 70, weight: '64 fl oz', brand: 'Orchard Pure', images: ['/images/products/apple-juice.jpg'] },
  { name: 'Cranberry Juice', price: 7.99, category: 'Beverages', description: '100% cranberry juice', stock: 50, weight: '64 fl oz', brand: 'Berry Pure', images: ['/images/products/cranberry-juice.jpg'] },
  { name: 'Grape Juice', price: 6.49, category: 'Beverages', description: 'Concord grape juice', stock: 60, weight: '64 fl oz', brand: 'Vineyard Select', images: ['/images/products/grape-juice.jpg'] },
  { name: 'Coca Cola', price: 7.99, category: 'Beverages', description: 'Classic Coca Cola', stock: 150, weight: '12 pack cans', brand: 'Coca Cola', images: ['/images/products/coca-cola.jpg'] },
  { name: 'Pepsi', price: 7.99, category: 'Beverages', description: 'Pepsi cola', stock: 120, weight: '12 pack cans', brand: 'Pepsi', images: ['/images/products/pepsi.jpg'] },
  { name: 'Sprite', price: 7.49, category: 'Beverages', description: 'Lemon-lime soda', stock: 100, weight: '12 pack cans', brand: 'Sprite', images: ['/images/products/sprite.jpg'] },
  { name: 'Ginger Ale', price: 6.99, category: 'Beverages', description: 'Canada Dry ginger ale', stock: 80, weight: '12 pack cans', brand: 'Canada Dry', images: ['/images/products/ginger-ale.jpg'] },
  { name: 'Energy Drink', price: 12.99, category: 'Beverages', description: 'Red Bull energy drink', stock: 60, weight: '4 pack', brand: 'Red Bull', images: ['/images/products/energy-drink.jpg'] },
  { name: 'Sports Drink', price: 8.99, category: 'Beverages', description: 'Gatorade sports drink', stock: 90, weight: '8 pack', brand: 'Gatorade', images: ['/images/products/sports-drink.jpg'] },
  { name: 'Iced Tea', price: 5.99, category: 'Beverages', description: 'Sweet iced tea', stock: 70, weight: '64 fl oz', brand: 'Southern Style', images: ['/images/products/iced-tea.jpg'] },
  { name: 'Green Tea', price: 4.99, category: 'Beverages', description: 'Organic green tea bags', stock: 80, weight: '20 bags', brand: 'Zen Tea', images: ['/images/products/green-tea.jpg'] },
  { name: 'Black Tea', price: 3.99, category: 'Beverages', description: 'English breakfast tea', stock: 90, weight: '20 bags', brand: 'Earl Grey', images: ['/images/products/black-tea.jpg'] },
  { name: 'Coffee Beans', price: 12.99, category: 'Beverages', description: 'Arabica coffee beans', stock: 50, weight: '12 oz', brand: 'Mountain Roast', images: ['/images/products/coffee-beans.jpg'] },
  { name: 'Ground Coffee', price: 9.99, category: 'Beverages', description: 'Medium roast ground coffee', stock: 70, weight: '12 oz', brand: 'Morning Brew', images: ['/images/products/ground-coffee.jpg'] },
  { name: 'Instant Coffee', price: 6.99, category: 'Beverages', description: 'Instant coffee granules', stock: 80, weight: '8 oz', brand: 'Quick Brew', images: ['/images/products/instant-coffee.jpg'] },
  { name: 'Hot Chocolate', price: 4.99, category: 'Beverages', description: 'Hot chocolate mix', stock: 60, weight: '12 oz', brand: 'Cozy Cocoa', images: ['/images/products/hot-chocolate.jpg'] },
  { name: 'Coconut Water', price: 8.99, category: 'Beverages', description: 'Natural coconut water', stock: 40, weight: '6 pack', brand: 'Tropical Pure', images: ['/images/products/coconut-water.jpg'] },
  { name: 'Kombucha', price: 12.99, category: 'Beverages', description: 'Probiotic kombucha', stock: 30, weight: '4 pack', brand: 'Gut Health', images: ['/images/products/kombucha.jpg'] },
  { name: 'Protein Shake', price: 15.99, category: 'Beverages', description: 'Ready-to-drink protein', stock: 40, weight: '4 pack', brand: 'Muscle Milk', images: ['/images/products/protein-shake.jpg'] },
  { name: 'Smoothie', price: 6.99, category: 'Beverages', description: 'Mixed berry smoothie', stock: 35, weight: '32 fl oz', brand: 'Fruit Blend', images: ['/images/products/smoothie.jpg'] },
  { name: 'Lemonade', price: 4.99, category: 'Beverages', description: 'Fresh lemonade', stock: 50, weight: '64 fl oz', brand: 'Summer Fresh', images: ['/images/products/lemonade.jpg'] },
  { name: 'Wine', price: 18.99, category: 'Beverages', description: 'Red wine bottle', stock: 25, weight: '750ml', brand: 'Vineyard Reserve', images: ['/images/products/wine.jpg'] },

  // Frozen Foods (15 items)
  { name: 'Frozen Pizza', price: 8.99, originalPrice: 9.99, category: 'Frozen Foods', description: 'Pepperoni pizza', stock: 60, weight: '12 inch', brand: 'Pizza Palace', images: ['/images/products/frozen-pizza.jpg'] },
  { name: 'Ice Cream', price: 6.99, category: 'Frozen Foods', description: 'Vanilla ice cream', stock: 80, weight: '1.5 qt', brand: 'Creamy Dreams', images: ['/images/products/ice-cream.jpg'] },
  { name: 'Frozen Vegetables', price: 3.99, category: 'Frozen Foods', description: 'Mixed vegetables', stock: 100, weight: '16 oz', brand: 'Garden Frozen', images: ['/images/products/frozen-vegetables.jpg'] },
  { name: 'Frozen Berries', price: 5.99, category: 'Frozen Foods', description: 'Mixed berries', stock: 70, weight: '12 oz', brand: 'Berry Frozen', images: ['/images/products/frozen-berries.jpg'] },
  { name: 'Frozen Chicken', price: 12.99, category: 'Frozen Foods', description: 'Frozen chicken breasts', stock: 50, weight: '2 lbs', brand: 'Frozen Fresh', images: ['/images/products/frozen-chicken.jpg'] },
  { name: 'Frozen Fish', price: 15.99, category: 'Frozen Foods', description: 'Frozen salmon fillets', stock: 40, weight: '1 lb', brand: 'Ocean Frozen', images: ['/images/products/frozen-fish.jpg'] },
  { name: 'Frozen Shrimp', price: 18.99, category: 'Frozen Foods', description: 'Frozen jumbo shrimp', stock: 35, weight: '1 lb', brand: 'Sea Frozen', images: ['/images/products/frozen-shrimp.jpg'] },
  { name: 'Frozen Fries', price: 4.99, category: 'Frozen Foods', description: 'Crispy french fries', stock: 90, weight: '2 lbs', brand: 'Golden Fries', images: ['/images/products/frozen-fries.jpg'] },
  { name: 'Frozen Waffles', price: 5.99, category: 'Frozen Foods', description: 'Buttermilk waffles', stock: 60, weight: '12 count', brand: 'Morning Crisp', images: ['/images/products/frozen-waffles.jpg'] },
  { name: 'Frozen Burritos', price: 7.99, category: 'Frozen Foods', description: 'Bean & cheese burritos', stock: 50, weight: '8 pack', brand: 'Mexican Frozen', images: ['/images/products/frozen-burritos.jpg'] },
  { name: 'Frozen Yogurt', price: 5.99, category: 'Frozen Foods', description: 'Strawberry frozen yogurt', stock: 40, weight: '1 qt', brand: 'Healthy Frozen', images: ['/images/products/frozen-yogurt.jpg'] },
  { name: 'Frozen Peas', price: 2.99, category: 'Frozen Foods', description: 'Green peas', stock: 80, weight: '16 oz', brand: 'Garden Frozen', images: ['/images/products/frozen-peas.jpg'] },
  { name: 'Frozen Corn', price: 2.99, category: 'Frozen Foods', description: 'Sweet corn kernels', stock: 75, weight: '16 oz', brand: 'Garden Frozen', images: ['/images/products/frozen-corn.jpg'] },
  { name: 'Frozen Spinach', price: 3.49, category: 'Frozen Foods', description: 'Chopped spinach', stock: 60, weight: '10 oz', brand: 'Leafy Frozen', images: ['/images/products/frozen-spinach.jpg'] },
  { name: 'Frozen Broccoli', price: 3.99, category: 'Frozen Foods', description: 'Broccoli florets', stock: 65, weight: '12 oz', brand: 'Green Frozen', images: ['/images/products/frozen-broccoli.jpg'] },

  // Health & Wellness (15 items)
  { name: 'Multivitamins', price: 19.99, category: 'Health & Wellness', description: 'Daily multivitamin tablets', stock: 50, weight: '100 tablets', brand: 'Vita Health', images: ['/images/products/multivitamins.jpg'] },
  { name: 'Vitamin C', price: 12.99, category: 'Health & Wellness', description: 'Vitamin C supplements', stock: 60, weight: '60 tablets', brand: 'Immune Boost', images: ['/images/products/vitamin-c.jpg'] },
  { name: 'Vitamin D', price: 14.99, category: 'Health & Wellness', description: 'Vitamin D3 capsules', stock: 45, weight: '90 capsules', brand: 'Sunshine Health', images: ['/images/products/vitamin-d.jpg'] },
  { name: 'Omega-3', price: 24.99, category: 'Health & Wellness', description: 'Fish oil omega-3', stock: 40, weight: '120 softgels', brand: 'Heart Health', images: ['/images/products/omega-3.jpg'] },
  { name: 'Probiotics', price: 29.99, originalPrice: 34.99, category: 'Health & Wellness', description: 'Probiotic capsules', stock: 35, weight: '30 capsules', brand: 'Gut Health', images: ['/images/products/probiotics.jpg'] },
  { name: 'Protein Powder', price: 39.99, category: 'Health & Wellness', description: 'Whey protein powder', stock: 30, weight: '2 lbs', brand: 'Muscle Builder', images: ['/images/products/protein-powder.jpg'] },
  { name: 'Organic Honey', price: 12.99, category: 'Health & Wellness', description: 'Raw organic honey', stock: 40, weight: '16 oz', brand: 'Pure Nature', images: ['/images/products/organic-honey.jpg'] },
  { name: 'Coconut Oil', price: 15.99, category: 'Health & Wellness', description: 'Virgin coconut oil', stock: 35, weight: '16 oz', brand: 'Tropical Pure', images: ['/images/products/coconut-oil.jpg'] },
  { name: 'Apple Cider Vinegar', price: 8.99, category: 'Health & Wellness', description: 'Organic apple cider vinegar', stock: 50, weight: '16 fl oz', brand: 'Nature\'s Best', images: ['/images/products/apple-cider-vinegar.jpg'] },
  { name: 'Herbal Tea', price: 6.99, category: 'Health & Wellness', description: 'Chamomile herbal tea', stock: 60, weight: '20 bags', brand: 'Herbal Wellness', images: ['/images/products/herbal-tea.jpg'] },
  { name: 'Turmeric Capsules', price: 18.99, category: 'Health & Wellness', description: 'Turmeric with black pepper', stock: 40, weight: '60 capsules', brand: 'Anti-Inflammatory', images: ['/images/products/turmeric-capsules.jpg'] },
  { name: 'Collagen Powder', price: 34.99, category: 'Health & Wellness', description: 'Hydrolyzed collagen', stock: 25, weight: '16 oz', brand: 'Beauty Health', images: ['/images/products/collagen-powder.jpg'] },
  { name: 'Magnesium', price: 16.99, category: 'Health & Wellness', description: 'Magnesium glycinate', stock: 45, weight: '90 capsules', brand: 'Mineral Health', images: ['/images/products/magnesium.jpg'] },
  { name: 'Zinc', price: 11.99, category: 'Health & Wellness', description: 'Zinc picolinate', stock: 50, weight: '60 tablets', brand: 'Immune Support', images: ['/images/products/zinc.jpg'] },
  { name: 'Melatonin', price: 9.99, category: 'Health & Wellness', description: 'Sleep support melatonin', stock: 55, weight: '60 tablets', brand: 'Sleep Well', images: ['/images/products/melatonin.jpg'] },

  // Baby Care (10 items)
  { name: 'Baby Formula', price: 24.99, category: 'Baby Care', description: 'Infant formula powder', stock: 40, weight: '12.4 oz', brand: 'Baby Nutrition', images: ['/images/products/baby-formula.jpg'] },
  { name: 'Baby Food', price: 8.99, category: 'Baby Care', description: 'Organic baby food jars', stock: 60, weight: '6 pack', brand: 'Little Ones', images: ['/images/products/baby-food.jpg'] },
  { name: 'Diapers', price: 29.99, originalPrice: 34.99, category: 'Baby Care', description: 'Size 3 diapers', stock: 50, weight: '84 count', brand: 'Comfort Dry', images: ['/images/products/diapers.jpg'] },
  { name: 'Baby Wipes', price: 12.99, category: 'Baby Care', description: 'Sensitive baby wipes', stock: 80, weight: '4 packs', brand: 'Gentle Care', images: ['/images/products/baby-wipes.jpg'] },
  { name: 'Baby Shampoo', price: 6.99, category: 'Baby Care', description: 'Tear-free baby shampoo', stock: 60, weight: '15 fl oz', brand: 'Baby Soft', images: ['/images/products/baby-shampoo.jpg'] },
  { name: 'Baby Lotion', price: 7.99, category: 'Baby Care', description: 'Moisturizing baby lotion', stock: 50, weight: '15 fl oz', brand: 'Baby Soft', images: ['/images/products/baby-lotion.jpg'] },
  { name: 'Baby Cereal', price: 5.99, category: 'Baby Care', description: 'Rice baby cereal', stock: 70, weight: '16 oz', brand: 'First Foods', images: ['/images/products/baby-cereal.jpg'] },
  { name: 'Pacifiers', price: 8.99, category: 'Baby Care', description: 'Silicone pacifiers', stock: 40, weight: '2 pack', brand: 'Soothe Baby', images: ['/images/products/pacifiers.jpg'] },
  { name: 'Baby Bottles', price: 15.99, category: 'Baby Care', description: 'Anti-colic baby bottles', stock: 35, weight: '3 pack', brand: 'Feed Easy', images: ['/images/products/baby-bottles.jpg'] },
  { name: 'Baby Powder', price: 4.99, category: 'Baby Care', description: 'Cornstarch baby powder', stock: 60, weight: '9 oz', brand: 'Dry Comfort', images: ['/images/products/baby-powder.jpg'] },

  // Household Items (15 items)
  { name: 'Dish Soap', price: 3.99, category: 'Household Items', description: 'Liquid dish soap', stock: 100, weight: '25 fl oz', brand: 'Clean Suds', images: ['/images/products/dish-soap.jpg'] },
  { name: 'Laundry Detergent', price: 12.99, originalPrice: 14.99, category: 'Household Items', description: 'Liquid laundry detergent', stock: 80, weight: '100 fl oz', brand: 'Fresh Clean', images: ['/images/products/laundry-detergent.jpg'] },
  { name: 'All-Purpose Cleaner', price: 4.99, category: 'Household Items', description: 'Multi-surface cleaner', stock: 90, weight: '32 fl oz', brand: 'Sparkle Clean', images: ['/images/products/all-purpose-cleaner.jpg'] },
  { name: 'Paper Towels', price: 8.99, category: 'Household Items', description: 'Absorbent paper towels', stock: 120, weight: '6 rolls', brand: 'Absorb Max', images: ['/images/products/paper-towels.jpg'] },
  { name: 'Toilet Paper', price: 15.99, category: 'Household Items', description: 'Ultra soft toilet paper', stock: 100, weight: '12 rolls', brand: 'Comfort Plus', images: ['/images/products/toilet-paper.jpg'] },
  { name: 'Trash Bags', price: 9.99, category: 'Household Items', description: 'Heavy duty trash bags', stock: 70, weight: '30 count', brand: 'Strong Hold', images: ['/images/products/trash-bags.jpg'] },
  { name: 'Aluminum Foil', price: 5.99, category: 'Household Items', description: 'Heavy duty aluminum foil', stock: 80, weight: '75 sq ft', brand: 'Wrap It', images: ['/images/products/aluminum-foil.jpg'] },
  { name: 'Plastic Wrap', price: 4.99, category: 'Household Items', description: 'Cling plastic wrap', stock: 75, weight: '100 sq ft', brand: 'Seal Fresh', images: ['/images/products/plastic-wrap.jpg'] },
  { name: 'Sponges', price: 3.99, category: 'Household Items', description: 'Kitchen scrub sponges', stock: 90, weight: '6 pack', brand: 'Scrub Clean', images: ['/images/products/sponges.jpg'] },
  { name: 'Bleach', price: 3.49, category: 'Household Items', description: 'Regular bleach', stock: 60, weight: '64 fl oz', brand: 'White Bright', images: ['/images/products/bleach.jpg'] },
  { name: 'Fabric Softener', price: 6.99, category: 'Household Items', description: 'Liquid fabric softener', stock: 70, weight: '64 fl oz', brand: 'Soft Touch', images: ['/images/products/fabric-softener.jpg'] },
  { name: 'Glass Cleaner', price: 4.49, category: 'Household Items', description: 'Streak-free glass cleaner', stock: 65, weight: '32 fl oz', brand: 'Crystal Clear', images: ['/images/products/glass-cleaner.jpg'] },
  { name: 'Disinfectant Spray', price: 5.99, category: 'Household Items', description: 'Antibacterial spray', stock: 80, weight: '19 fl oz', brand: 'Germ Kill', images: ['/images/products/disinfectant-spray.jpg'] },
  { name: 'Air Freshener', price: 3.99, category: 'Household Items', description: 'Room air freshener', stock: 85, weight: '9 oz', brand: 'Fresh Scent', images: ['/images/products/air-freshener.jpg'] },
  { name: 'Vacuum Bags', price: 7.99, category: 'Household Items', description: 'Universal vacuum bags', stock: 40, weight: '5 pack', brand: 'Clean Sweep', images: ['/images/products/vacuum-bags.jpg'] }
];

// Function to seed categories
async function seedCategories() {
  try {
    // Get admin user
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      throw new Error('Admin user not found. Please create an admin user first.');
    }

    await Category.deleteMany({});

    // Add createdBy field to categories
    const categoriesWithCreator = categories.map(category => ({
      ...category,
      createdBy: adminUser._id
    }));

    const createdCategories = await Category.insertMany(categoriesWithCreator);
    console.log(`✅ Created ${createdCategories.length} categories`);
    return createdCategories;
  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    throw error;
  }
}

// Function to seed products
async function seedProducts(categoryMap) {
  try {
    // Get admin user
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      throw new Error('Admin user not found. Please create an admin user first.');
    }

    await Product.deleteMany({});

    // Map products to category IDs and fix structure
    const productsWithCategories = products.map(product => ({
      name: product.name,
      description: product.description,
      price: Math.round(product.price * 83), // Convert USD to INR (1 USD ≈ 83 INR)
      originalPrice: Math.round((product.originalPrice || product.price) * 83),
      category: categoryMap[product.category],
      brand: product.brand,
      sku: `BGS-${Math.random().toString(36).substr(2, 9).toUpperCase()}`,
      images: product.images ? product.images.map(img => ({ url: img, alt: product.name, isPrimary: true })) : [{ url: '/api/placeholder/300/300', alt: product.name, isPrimary: true }],
      stock: {
        quantity: product.stock || 50,
        lowStockThreshold: 10,
        trackInventory: true
      },
      specifications: {
        weight: {
          value: 1,
          unit: 'kg'
        }
      },
      rating: {
        average: Math.random() * 2 + 3, // Random rating between 3-5
        count: Math.floor(Math.random() * 100) + 1 // Random reviews 1-100
      },
      isActive: true,
      isFeatured: Math.random() > 0.8, // 20% chance to be featured
      createdBy: adminUser._id,
      seo: {
        slug: product.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
      }
    }));

    const createdProducts = await Product.insertMany(productsWithCategories);
    console.log(`✅ Created ${createdProducts.length} products`);
    return createdProducts;
  } catch (error) {
    console.error('❌ Error seeding products:', error);
    throw error;
  }
}

// Main seeding function
async function seedDatabase() {
  try {
    console.log('🌱 Starting database seeding...');
    
    // Seed categories first
    const createdCategories = await seedCategories();
    
    // Create category name to ID mapping
    const categoryMap = {};
    createdCategories.forEach(cat => {
      categoryMap[cat.name] = cat._id;
    });
    
    // Seed products
    await seedProducts(categoryMap);
    
    console.log('🎉 Database seeding completed successfully!');
    console.log(`📊 Total items: ${products.length} products across ${categories.length} categories`);
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the seeding
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
