{"name": "get-package-type", "version": "0.1.0", "description": "Determine the `package.json#type` which applies to a location", "type": "module", "main": "index.cjs", "exports": "./index.cjs", "scripts": {"pretest": "if-ver -ge 10 || exit 0; cfware-lint .", "tests-only": "nyc -s node test.cjs", "test": "npm run -s tests-only", "posttest": "nyc report --check-coverage"}, "engines": {"node": ">=8.0.0"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/cfware/get-package-type.git"}, "bugs": {"url": "https://github.com/cfware/get-package-type/issues"}, "homepage": "https://github.com/cfware/get-package-type#readme", "dependencies": {}, "devDependencies": {"@cfware/lint": "^1.4.3", "@cfware/nyc": "^0.7.0", "if-ver": "^1.1.0", "libtap": "^0.3.0", "nyc": "^15.0.1"}}