const Product = require('../models/Product');
const Category = require('../models/Category');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Get all products with filtering, sorting, and pagination
// @route   GET /api/products
// @access  Public
const getProducts = asyncHandler(async (req, res, next) => {
  const {
    page = 1,
    limit = 12,
    category,
    minPrice,
    maxPrice,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    featured,
    inStock
  } = req.query;

  // Build query
  const query = { isActive: true };

  // Category filter
  if (category) {
    query.category = category;
  }

  // Price range filter
  if (minPrice || maxPrice) {
    query.price = {};
    if (minPrice) query.price.$gte = parseFloat(minPrice);
    if (maxPrice) query.price.$lte = parseFloat(maxPrice);
  }

  // Search filter
  if (search && search.trim()) {
    query.$text = { $search: search.trim() };
  }

  // Featured filter
  if (featured === 'true') {
    query.isFeatured = true;
  }

  // Stock filter
  if (inStock === 'true') {
    query['stock.quantity'] = { $gt: 0 };
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Execute query
  const products = await Product.find(query)
    .populate('category', 'name slug')
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .select('-reviews -createdBy -updatedBy');

  // Get total count for pagination
  const total = await Product.countDocuments(query);

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    totalPages: Math.ceil(total / parseInt(limit)),
    currentPage: parseInt(page),
    products
  });
});

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
const getProduct = asyncHandler(async (req, res, next) => {
  const product = await Product.findById(req.params.id)
    .populate('category', 'name slug')
    .populate('reviews.user', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

  if (!product || !product.isActive) {
    return next(new ErrorResponse('Product not found', 404));
  }

  res.status(200).json({
    success: true,
    product
  });
});

// @desc    Get product by slug
// @route   GET /api/products/slug/:slug
// @access  Public
const getProductBySlug = asyncHandler(async (req, res, next) => {
  const product = await Product.findOne({ 
    'seo.slug': req.params.slug,
    isActive: true 
  })
    .populate('category', 'name slug')
    .populate('reviews.user', 'firstName lastName')
    .populate('createdBy', 'firstName lastName');

  if (!product) {
    return next(new ErrorResponse('Product not found', 404));
  }

  res.status(200).json({
    success: true,
    product
  });
});

// @desc    Get featured products
// @route   GET /api/products/featured
// @access  Public
const getFeaturedProducts = asyncHandler(async (req, res, next) => {
  const { limit = 8 } = req.query;

  const products = await Product.findFeatured()
    .populate('category', 'name slug')
    .limit(parseInt(limit))
    .select('-reviews -createdBy -updatedBy');

  res.status(200).json({
    success: true,
    count: products.length,
    products
  });
});

// @desc    Get products by category
// @route   GET /api/products/category/:categoryId
// @access  Public
const getProductsByCategory = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 12, sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

  // Check if category exists
  const category = await Category.findById(req.params.categoryId);
  if (!category) {
    return next(new ErrorResponse('Category not found', 404));
  }

  // Build sort object
  const sort = {};
  sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

  // Calculate pagination
  const skip = (parseInt(page) - 1) * parseInt(limit);

  const products = await Product.findByCategory(req.params.categoryId)
    .populate('category', 'name slug')
    .sort(sort)
    .skip(skip)
    .limit(parseInt(limit))
    .select('-reviews -createdBy -updatedBy');

  const total = await Product.countDocuments({ 
    category: req.params.categoryId, 
    isActive: true 
  });

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    totalPages: Math.ceil(total / parseInt(limit)),
    currentPage: parseInt(page),
    category: category.name,
    products
  });
});

// @desc    Search products
// @route   GET /api/products/search
// @access  Public
const searchProducts = asyncHandler(async (req, res, next) => {
  const { q, page = 1, limit = 12 } = req.query;

  if (!q) {
    return next(new ErrorResponse('Search query is required', 400));
  }

  const skip = (parseInt(page) - 1) * parseInt(limit);

  // Text search with scoring
  const products = await Product.find(
    { 
      $text: { $search: q },
      isActive: true 
    },
    { score: { $meta: 'textScore' } }
  )
    .populate('category', 'name slug')
    .sort({ score: { $meta: 'textScore' } })
    .skip(skip)
    .limit(parseInt(limit))
    .select('-reviews -createdBy -updatedBy');

  const total = await Product.countDocuments({
    $text: { $search: q },
    isActive: true
  });

  res.status(200).json({
    success: true,
    count: products.length,
    total,
    totalPages: Math.ceil(total / parseInt(limit)),
    currentPage: parseInt(page),
    searchQuery: q,
    products
  });
});

// @desc    Add product review
// @route   POST /api/products/:id/reviews
// @access  Private
const addProductReview = asyncHandler(async (req, res, next) => {
  const { rating, comment } = req.body;

  const product = await Product.findById(req.params.id);
  if (!product) {
    return next(new ErrorResponse('Product not found', 404));
  }

  // Check if user already reviewed this product
  const existingReview = product.reviews.find(
    review => review.user.toString() === req.user._id.toString()
  );

  if (existingReview) {
    return next(new ErrorResponse('You have already reviewed this product', 400));
  }

  // Add review
  product.reviews.push({
    user: req.user._id,
    rating: parseInt(rating),
    comment
  });

  // Recalculate average rating
  product.calculateAverageRating();
  await product.save();

  res.status(201).json({
    success: true,
    message: 'Review added successfully'
  });
});

// @desc    Get product reviews
// @route   GET /api/products/:id/reviews
// @access  Public
const getProductReviews = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10 } = req.query;

  const product = await Product.findById(req.params.id)
    .populate('reviews.user', 'firstName lastName')
    .select('reviews rating');

  if (!product) {
    return next(new ErrorResponse('Product not found', 404));
  }

  // Paginate reviews
  const skip = (parseInt(page) - 1) * parseInt(limit);
  const reviews = product.reviews
    .sort((a, b) => b.createdAt - a.createdAt)
    .slice(skip, skip + parseInt(limit));

  res.status(200).json({
    success: true,
    count: reviews.length,
    total: product.reviews.length,
    totalPages: Math.ceil(product.reviews.length / parseInt(limit)),
    currentPage: parseInt(page),
    averageRating: product.rating.average,
    totalReviews: product.rating.count,
    reviews
  });
});

// @desc    Get related products
// @route   GET /api/products/:id/related
// @access  Public
const getRelatedProducts = asyncHandler(async (req, res, next) => {
  const { limit = 4 } = req.query;

  const product = await Product.findById(req.params.id);
  if (!product) {
    return next(new ErrorResponse('Product not found', 404));
  }

  // Find products in the same category, excluding current product
  const relatedProducts = await Product.find({
    category: product.category,
    _id: { $ne: product._id },
    isActive: true
  })
    .populate('category', 'name slug')
    .limit(parseInt(limit))
    .select('-reviews -createdBy -updatedBy');

  res.status(200).json({
    success: true,
    count: relatedProducts.length,
    products: relatedProducts
  });
});

module.exports = {
  getProducts,
  getProduct,
  getProductBySlug,
  getFeaturedProducts,
  getProductsByCategory,
  searchProducts,
  addProductReview,
  getProductReviews,
  getRelatedProducts
};
