{"ast": null, "code": "import api from './api';\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Remove item from cart\n  removeFromCart: async productId => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n  // Apply coupon\n  applyCoupon: async couponCode => {\n    const response = await api.post('/cart/coupon/apply', {\n      couponCode\n    });\n    return response.data;\n  },\n  // Remove coupon\n  removeCoupon: async () => {\n    const response = await api.delete('/cart/coupon/remove');\n    return response.data;\n  },\n  // Validate cart\n  validateCart: async token => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  }\n};\nexport default cartService;", "map": {"version": 3, "names": ["api", "cartService", "getCart", "response", "get", "data", "addToCart", "productId", "quantity", "post", "updateCartItem", "put", "removeFromCart", "delete", "clearCart", "applyCoupon", "couponCode", "removeCoupon", "validateCart", "token", "headers", "Authorization"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/cartService.js"], "sourcesContent": ["import api from './api';\n\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', { productId, quantity });\n    return response.data;\n  },\n\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', { productId, quantity });\n    return response.data;\n  },\n\n  // Remove item from cart\n  removeFromCart: async (productId) => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n\n  // Apply coupon\n  applyCoupon: async (couponCode) => {\n    const response = await api.post('/cart/coupon/apply', { couponCode });\n    return response.data;\n  },\n\n  // Remove coupon\n  removeCoupon: async () => {\n    const response = await api.delete('/cart/coupon/remove');\n    return response.data;\n  },\n\n  // Validate cart\n  validateCart: async (token) => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n};\n\nexport default cartService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,OAAO,CAAC;IACvC,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,KAAK;IACxC,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,WAAW,EAAE;MAAEF,SAAS;MAAEC;IAAS,CAAC,CAAC;IACrE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAK,cAAc,EAAE,MAAAA,CAAOH,SAAS,EAAEC,QAAQ,KAAK;IAC7C,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACW,GAAG,CAAC,cAAc,EAAE;MAAEJ,SAAS;MAAEC;IAAS,CAAC,CAAC;IACvE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,cAAc,EAAE,MAAOL,SAAS,IAAK;IACnC,MAAMJ,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,gBAAgBN,SAAS,EAAE,CAAC;IAC9D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAMX,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,aAAa,CAAC;IAChD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,WAAW,EAAE,MAAOC,UAAU,IAAK;IACjC,MAAMb,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,oBAAoB,EAAE;MAAEO;IAAW,CAAC,CAAC;IACrE,OAAOb,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAY,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,MAAMd,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,qBAAqB,CAAC;IACxD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAa,YAAY,EAAE,MAAOC,KAAK,IAAK;IAC7B,MAAMhB,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE;MACpDW,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUF,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOhB,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}