const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Ensure upload directories exist
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// Configure storage for different file types
const createStorage = (uploadPath) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      const fullPath = path.join(__dirname, '..', uploadPath);
      ensureDirectoryExists(fullPath);
      cb(null, fullPath);
    },
    filename: (req, file, cb) => {
      // Generate unique filename
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
      const extension = path.extname(file.originalname);
      const filename = file.fieldname + '-' + uniqueSuffix + extension;
      cb(null, filename);
    }
  });
};

// File filter for images
const imageFilter = (req, file, cb) => {
  // Check file type
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'), false);
  }
};

// File filter for documents
const documentFilter = (req, file, cb) => {
  const allowedTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Only PDF, DOC, DOCX, and TXT files are allowed'), false);
  }
};

// Product image upload configuration
const productImageUpload = multer({
  storage: createStorage('uploads/products'),
  fileFilter: imageFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5 // Maximum 5 files
  }
});

// User avatar upload configuration
const avatarUpload = multer({
  storage: createStorage('uploads/avatars'),
  fileFilter: imageFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // 2MB limit
    files: 1 // Single file only
  }
});

// Category image upload configuration
const categoryImageUpload = multer({
  storage: createStorage('uploads/categories'),
  fileFilter: imageFilter,
  limits: {
    fileSize: 3 * 1024 * 1024, // 3MB limit
    files: 1 // Single file only
  }
});

// General document upload configuration
const documentUpload = multer({
  storage: createStorage('uploads/documents'),
  fileFilter: documentFilter,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 3 // Maximum 3 files
  }
});

// Memory storage for temporary processing
const memoryUpload = multer({
  storage: multer.memoryStorage(),
  fileFilter: imageFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 5
  }
});

// Middleware to handle upload errors
const handleUploadError = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    let message = 'File upload error';
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        message = 'File too large';
        break;
      case 'LIMIT_FILE_COUNT':
        message = 'Too many files';
        break;
      case 'LIMIT_FIELD_KEY':
        message = 'Field name too long';
        break;
      case 'LIMIT_FIELD_VALUE':
        message = 'Field value too long';
        break;
      case 'LIMIT_FIELD_COUNT':
        message = 'Too many fields';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        message = 'Unexpected file field';
        break;
    }
    
    return res.status(400).json({
      success: false,
      message,
      error: error.code
    });
  }
  
  if (error) {
    return res.status(400).json({
      success: false,
      message: error.message || 'File upload error'
    });
  }
  
  next();
};

// Middleware to process uploaded files
const processUploadedFiles = (req, res, next) => {
  if (req.files) {
    // Handle multiple files (array)
    if (Array.isArray(req.files)) {
      req.uploadedFiles = req.files.map(file => ({
        fieldname: file.fieldname,
        originalname: file.originalname,
        filename: file.filename,
        path: file.path,
        size: file.size,
        mimetype: file.mimetype,
        url: `/uploads/${path.basename(path.dirname(file.path))}/${file.filename}`
      }));
    }
    // Handle multiple files (object with field names)
    else if (typeof req.files === 'object') {
      req.uploadedFiles = {};
      Object.keys(req.files).forEach(fieldname => {
        const files = req.files[fieldname];
        req.uploadedFiles[fieldname] = files.map(file => ({
          fieldname: file.fieldname,
          originalname: file.originalname,
          filename: file.filename,
          path: file.path,
          size: file.size,
          mimetype: file.mimetype,
          url: `/uploads/${path.basename(path.dirname(file.path))}/${file.filename}`
        }));
      });
    }
  }
  
  // Handle single file
  if (req.file) {
    req.uploadedFile = {
      fieldname: req.file.fieldname,
      originalname: req.file.originalname,
      filename: req.file.filename,
      path: req.file.path,
      size: req.file.size,
      mimetype: req.file.mimetype,
      url: `/uploads/${path.basename(path.dirname(req.file.path))}/${req.file.filename}`
    };
  }
  
  next();
};

// Utility function to delete uploaded file
const deleteUploadedFile = (filePath) => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
};

// Utility function to delete multiple files
const deleteUploadedFiles = (filePaths) => {
  const results = [];
  filePaths.forEach(filePath => {
    results.push(deleteUploadedFile(filePath));
  });
  return results;
};

// Middleware to clean up files on error
const cleanupOnError = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // If there's an error response and files were uploaded, clean them up
    if (res.statusCode >= 400) {
      if (req.file) {
        deleteUploadedFile(req.file.path);
      }
      if (req.files) {
        if (Array.isArray(req.files)) {
          req.files.forEach(file => deleteUploadedFile(file.path));
        } else if (typeof req.files === 'object') {
          Object.values(req.files).forEach(files => {
            files.forEach(file => deleteUploadedFile(file.path));
          });
        }
      }
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  productImageUpload,
  avatarUpload,
  categoryImageUpload,
  documentUpload,
  memoryUpload,
  handleUploadError,
  processUploadedFiles,
  deleteUploadedFile,
  deleteUploadedFiles,
  cleanupOnError
};
