// Currency formatting utilities

/**
 * Format price in Indian Rupees
 * @param {number} amount - The amount to format
 * @param {boolean} showSymbol - Whether to show the ₹ symbol
 * @returns {string} Formatted price string
 */
export const formatPrice = (amount, showSymbol = true) => {
  if (typeof amount !== 'number' || isNaN(amount)) {
    return showSymbol ? '₹0' : '0';
  }

  // Format number with Indian number system (lakhs, crores)
  const formatter = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });

  if (showSymbol) {
    return formatter.format(amount);
  } else {
    return formatter.format(amount).replace('₹', '').trim();
  }
};

/**
 * Format price without currency symbol
 * @param {number} amount - The amount to format
 * @returns {string} Formatted price string without symbol
 */
export const formatPriceNumber = (amount) => {
  return formatPrice(amount, false);
};

/**
 * Calculate discount percentage
 * @param {number} originalPrice - Original price
 * @param {number} currentPrice - Current price
 * @returns {number} Discount percentage
 */
export const calculateDiscountPercentage = (originalPrice, currentPrice) => {
  if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {
    return 0;
  }
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
};

/**
 * Format discount amount
 * @param {number} originalPrice - Original price
 * @param {number} currentPrice - Current price
 * @returns {string} Formatted discount amount
 */
export const formatDiscountAmount = (originalPrice, currentPrice) => {
  const discount = originalPrice - currentPrice;
  return discount > 0 ? formatPrice(discount) : '₹0';
};
