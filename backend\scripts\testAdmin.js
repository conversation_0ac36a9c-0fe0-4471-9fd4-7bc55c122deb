const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = require('../models/User');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grocery-store')
  .then(() => console.log('✅ MongoDB connected for admin test'))
  .catch(err => console.error('❌ MongoDB connection error:', err));

async function testAdmin() {
  try {
    // Find admin user (explicitly include password field)
    const admin = await User.findOne({ email: '<EMAIL>' }).select('+password');
    
    if (!admin) {
      console.log('❌ Admin user not found');
      return;
    }
    
    console.log('✅ Admin user found:');
    console.log('📧 Email:', admin.email);
    console.log('👑 Role:', admin.role);
    console.log('🔓 Is Active:', admin.isActive);
    console.log('🔑 Password exists:', !!admin.password);
    console.log('🔑 Password Hash:', admin.password ? admin.password.substring(0, 20) + '...' : 'NO PASSWORD');

    if (admin.password) {
      // Test password comparison
      const testPassword = 'admin123';
      console.log('🔐 Testing password:', `"${testPassword}"`);
      console.log('🔐 Password length:', testPassword.length);

      const isValid = await admin.comparePassword(testPassword);
      console.log('🔐 Password test result:', isValid);

      // Test manual bcrypt comparison
      const manualTest = await bcrypt.compare(testPassword, admin.password);
      console.log('🔐 Manual bcrypt test:', manualTest);

      // Create a fresh hash and compare
      console.log('🔧 Creating fresh hash for comparison...');
      const salt = await bcrypt.genSalt(12);
      const freshHash = await bcrypt.hash('admin123', salt);
      console.log('🔑 Fresh hash:', freshHash.substring(0, 20) + '...');

      const freshTest = await bcrypt.compare('admin123', freshHash);
      console.log('🔐 Fresh hash test:', freshTest);

      // Check if the stored hash is valid
      console.log('🔍 Stored hash format check:', admin.password.startsWith('$2a$') || admin.password.startsWith('$2b$'));
    } else {
      console.log('❌ No password hash found - admin user is corrupted');
    }
    
  } catch (error) {
    console.error('❌ Error testing admin:', error);
  } finally {
    mongoose.connection.close();
  }
}

testAdmin();
