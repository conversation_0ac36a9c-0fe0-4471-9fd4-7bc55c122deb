{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\components\\\\debug\\\\CartDebug.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { getCart, addToCart } from '../../store/slices/cartSlice';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CartDebug = () => {\n  _s();\n  var _auth$user, _cart$items;\n  const dispatch = useDispatch();\n  const cart = useSelector(state => state.cart);\n  const auth = useSelector(state => state.auth);\n  const handleTestAddToCart = async () => {\n    try {\n      console.log('Testing add to cart...');\n      const result = await dispatch(addToCart({\n        productId: '688c5d7adb720a4c64937040',\n        // Test product ID\n        quantity: 1\n      })).unwrap();\n      console.log('Add to cart result:', result);\n    } catch (error) {\n      console.error('Add to cart error:', error);\n    }\n  };\n  const handleTestGetCart = async () => {\n    try {\n      console.log('Testing get cart...');\n      const result = await dispatch(getCart()).unwrap();\n      console.log('Get cart result:', result);\n    } catch (error) {\n      console.error('Get cart error:', error);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 bg-gray-100 border rounded\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-bold mb-4\",\n      children: \"Cart Debug Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold\",\n        children: \"Auth State:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        className: \"text-xs bg-white p-2 rounded\",\n        children: JSON.stringify({\n          user: (_auth$user = auth.user) === null || _auth$user === void 0 ? void 0 : _auth$user.email,\n          token: auth.token ? 'Present' : 'Missing',\n          isLoggedIn: !!auth.user\n        }, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold\",\n        children: \"Cart State:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        className: \"text-xs bg-white p-2 rounded\",\n        children: JSON.stringify({\n          totalItems: cart.totalItems,\n          itemsCount: ((_cart$items = cart.items) === null || _cart$items === void 0 ? void 0 : _cart$items.length) || 0,\n          isLoading: cart.isLoading,\n          isError: cart.isError,\n          message: cart.message\n        }, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-x-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleTestGetCart,\n        className: \"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n        children: \"Test Get Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleTestAddToCart,\n        className: \"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\",\n        children: \"Test Add to Cart\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), cart.items && cart.items.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"font-semibold\",\n        children: \"Cart Items:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n        className: \"text-xs bg-white p-2 rounded\",\n        children: JSON.stringify(cart.items, null, 2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(CartDebug, \"9XM7Dn38qEaiswYHFiDkGqbKnUw=\", false, function () {\n  return [useDispatch, useSelector, useSelector];\n});\n_c = CartDebug;\nexport default CartDebug;\nvar _c;\n$RefreshReg$(_c, \"CartDebug\");", "map": {"version": 3, "names": ["React", "useSelector", "useDispatch", "getCart", "addToCart", "jsxDEV", "_jsxDEV", "CartDebug", "_s", "_auth$user", "_cart$items", "dispatch", "cart", "state", "auth", "handleTestAddToCart", "console", "log", "result", "productId", "quantity", "unwrap", "error", "handleTestGetCart", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "JSON", "stringify", "user", "email", "token", "isLoggedIn", "totalItems", "itemsCount", "items", "length", "isLoading", "isError", "message", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/components/debug/CartDebug.js"], "sourcesContent": ["import React from 'react';\nimport { useSelector, useDispatch } from 'react-redux';\nimport { getCart, addToCart } from '../../store/slices/cartSlice';\n\nconst CartDebug = () => {\n  const dispatch = useDispatch();\n  const cart = useSelector(state => state.cart);\n  const auth = useSelector(state => state.auth);\n\n  const handleTestAddToCart = async () => {\n    try {\n      console.log('Testing add to cart...');\n      const result = await dispatch(addToCart({\n        productId: '688c5d7adb720a4c64937040', // Test product ID\n        quantity: 1\n      })).unwrap();\n      console.log('Add to cart result:', result);\n    } catch (error) {\n      console.error('Add to cart error:', error);\n    }\n  };\n\n  const handleTestGetCart = async () => {\n    try {\n      console.log('Testing get cart...');\n      const result = await dispatch(getCart()).unwrap();\n      console.log('Get cart result:', result);\n    } catch (error) {\n      console.error('Get cart error:', error);\n    }\n  };\n\n  return (\n    <div className=\"p-4 bg-gray-100 border rounded\">\n      <h3 className=\"text-lg font-bold mb-4\">Cart Debug Panel</h3>\n      \n      <div className=\"mb-4\">\n        <h4 className=\"font-semibold\">Auth State:</h4>\n        <pre className=\"text-xs bg-white p-2 rounded\">\n          {JSON.stringify({ \n            user: auth.user?.email, \n            token: auth.token ? 'Present' : 'Missing',\n            isLoggedIn: !!auth.user \n          }, null, 2)}\n        </pre>\n      </div>\n\n      <div className=\"mb-4\">\n        <h4 className=\"font-semibold\">Cart State:</h4>\n        <pre className=\"text-xs bg-white p-2 rounded\">\n          {JSON.stringify({\n            totalItems: cart.totalItems,\n            itemsCount: cart.items?.length || 0,\n            isLoading: cart.isLoading,\n            isError: cart.isError,\n            message: cart.message\n          }, null, 2)}\n        </pre>\n      </div>\n\n      <div className=\"space-x-2\">\n        <button \n          onClick={handleTestGetCart}\n          className=\"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\"\n        >\n          Test Get Cart\n        </button>\n        <button \n          onClick={handleTestAddToCart}\n          className=\"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600\"\n        >\n          Test Add to Cart\n        </button>\n      </div>\n\n      {cart.items && cart.items.length > 0 && (\n        <div className=\"mt-4\">\n          <h4 className=\"font-semibold\">Cart Items:</h4>\n          <pre className=\"text-xs bg-white p-2 rounded\">\n            {JSON.stringify(cart.items, null, 2)}\n          </pre>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default CartDebug;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,EAAEC,SAAS,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,UAAA,EAAAC,WAAA;EACtB,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,IAAI,GAAGX,WAAW,CAACY,KAAK,IAAIA,KAAK,CAACD,IAAI,CAAC;EAC7C,MAAME,IAAI,GAAGb,WAAW,CAACY,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC;EAE7C,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;MACrC,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACP,SAAS,CAAC;QACtCe,SAAS,EAAE,0BAA0B;QAAE;QACvCC,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACZL,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,MAAM,CAAC;IAC5C,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;MAClC,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACR,OAAO,CAAC,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC;MACjDL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,MAAM,CAAC;IACzC,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdN,OAAO,CAACM,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;IACzC;EACF,CAAC;EAED,oBACEhB,OAAA;IAAKkB,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7CnB,OAAA;MAAIkB,SAAS,EAAC,wBAAwB;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAE5DvB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBnB,OAAA;QAAIkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9CvB,OAAA;QAAKkB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAC1CK,IAAI,CAACC,SAAS,CAAC;UACdC,IAAI,GAAAvB,UAAA,GAAEK,IAAI,CAACkB,IAAI,cAAAvB,UAAA,uBAATA,UAAA,CAAWwB,KAAK;UACtBC,KAAK,EAAEpB,IAAI,CAACoB,KAAK,GAAG,SAAS,GAAG,SAAS;UACzCC,UAAU,EAAE,CAAC,CAACrB,IAAI,CAACkB;QACrB,CAAC,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBnB,OAAA;QAAIkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9CvB,OAAA;QAAKkB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAC1CK,IAAI,CAACC,SAAS,CAAC;UACdK,UAAU,EAAExB,IAAI,CAACwB,UAAU;UAC3BC,UAAU,EAAE,EAAA3B,WAAA,GAAAE,IAAI,CAAC0B,KAAK,cAAA5B,WAAA,uBAAVA,WAAA,CAAY6B,MAAM,KAAI,CAAC;UACnCC,SAAS,EAAE5B,IAAI,CAAC4B,SAAS;UACzBC,OAAO,EAAE7B,IAAI,CAAC6B,OAAO;UACrBC,OAAO,EAAE9B,IAAI,CAAC8B;QAChB,CAAC,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBnB,OAAA;QACEqC,OAAO,EAAEpB,iBAAkB;QAC3BC,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACvE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvB,OAAA;QACEqC,OAAO,EAAE5B,mBAAoB;QAC7BS,SAAS,EAAC,8DAA8D;QAAAC,QAAA,EACzE;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,EAELjB,IAAI,CAAC0B,KAAK,IAAI1B,IAAI,CAAC0B,KAAK,CAACC,MAAM,GAAG,CAAC,iBAClCjC,OAAA;MAAKkB,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBnB,OAAA;QAAIkB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC9CvB,OAAA;QAAKkB,SAAS,EAAC,8BAA8B;QAAAC,QAAA,EAC1CK,IAAI,CAACC,SAAS,CAACnB,IAAI,CAAC0B,KAAK,EAAE,IAAI,EAAE,CAAC;MAAC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrB,EAAA,CAjFID,SAAS;EAAA,QACIL,WAAW,EACfD,WAAW,EACXA,WAAW;AAAA;AAAA2C,EAAA,GAHpBrC,SAAS;AAmFf,eAAeA,SAAS;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}