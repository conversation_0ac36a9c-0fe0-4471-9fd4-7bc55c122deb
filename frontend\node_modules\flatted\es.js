self.Flatted=function(t){"use strict";const{parse:e,stringify:n}=JSON,{keys:r}=Object,s=String,o="string",c={},l="object",a=(t,e)=>e,f=t=>t instanceof s?s(t):t,i=(t,e)=>typeof e===o?new s(e):e,u=(t,e,n,o)=>{const a=[];for(let f=r(n),{length:i}=f,u=0;u<i;u++){const r=f[u],i=n[r];if(i instanceof s){const s=t[i];typeof s!==l||e.has(s)?n[r]=o.call(n,r,s):(e.add(s),n[r]=c,a.push({k:r,a:[t,e,s,o]}))}else n[r]!==c&&(n[r]=o.call(n,r,i))}for(let{length:t}=a,e=0;e<t;e++){const{k:t,a:r}=a[e];n[t]=o.call(n,t,u.apply(null,r))}return n},p=(t,e,n)=>{const r=s(e.push(n)-1);return t.set(n,r),r},y=(t,n)=>{const r=e(t,i).map(f),s=r[0],o=n||a,c=typeof s===l&&s?u(r,new Set,s,o):s;return o.call({"":c},"",c)},g=(t,e,r)=>{const s=e&&typeof e===l?(t,n)=>""===t||-1<e.indexOf(t)?n:void 0:e||a,c=new Map,f=[],i=[];let u=+p(c,f,s.call({"":t},"",t)),y=!u;for(;u<f.length;)y=!0,i[u]=n(f[u++],g,r);return"["+i.join(",")+"]";function g(t,e){if(y)return y=!y,e;const n=s.call(this,t,e);switch(typeof n){case l:if(null===n)return n;case o:return c.get(n)||p(c,f,n)}return n}};return t.fromJSON=t=>y(n(t)),t.parse=y,t.stringify=g,t.toJSON=t=>e(g(t)),t}({});
