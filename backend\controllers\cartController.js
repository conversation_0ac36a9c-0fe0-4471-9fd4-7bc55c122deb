const Cart = require('../models/Cart');
const Product = require('../models/Product');
const Coupon = require('../models/Coupon');
const { asyncHandler } = require('../middleware/errorHandler');
const { ErrorResponse } = require('../middleware/errorHandler');

// @desc    Get user cart
// @route   GET /api/cart
// @access  Private
const getCart = asyncHandler(async (req, res, next) => {
  const cart = await Cart.getCartWithProducts(req.user._id);
  
  if (!cart) {
    return res.status(200).json({
      success: true,
      cart: {
        items: [],
        totalItems: 0,
        totalPrice: 0,
        finalPrice: 0,
        appliedCoupons: []
      }
    });
  }

  // Validate cart items
  const { validItems, invalidItems } = await cart.validateItems();
  
  if (invalidItems.length > 0) {
    // Remove invalid items and save cart
    cart.items = validItems;
    await cart.save();
  }

  res.status(200).json({
    success: true,
    cart: cart.getSummary(),
    items: cart.items,
    invalidItems: invalidItems.length > 0 ? invalidItems : undefined
  });
});

// @desc    Add item to cart
// @route   POST /api/cart/add
// @access  Private
const addToCart = asyncHandler(async (req, res, next) => {
  const { productId, quantity = 1 } = req.body;

  // Check if product exists and is active
  const product = await Product.findById(productId);
  if (!product || !product.isActive) {
    return next(new ErrorResponse('Product not found', 404));
  }

  // Check stock availability
  if (product.stock.trackInventory && product.stock.quantity < quantity) {
    return next(new ErrorResponse(`Only ${product.stock.quantity} items available in stock`, 400));
  }

  // Get or create cart
  const cart = await Cart.findOrCreateCart(req.user._id);

  // Add item to cart
  await cart.addItem(productId, quantity, product.price);

  // Get updated cart with product details
  const updatedCart = await Cart.getCartWithProducts(req.user._id);

  res.status(200).json({
    success: true,
    message: 'Item added to cart successfully',
    cart: updatedCart.getSummary(),
    items: updatedCart.items
  });
});

// @desc    Update cart item quantity
// @route   PUT /api/cart/update/:productId
// @access  Private
const updateCartItem = asyncHandler(async (req, res, next) => {
  const { productId } = req.params;
  const { quantity } = req.body;

  // Get cart
  const cart = await Cart.findOne({ user: req.user._id, isActive: true });
  if (!cart) {
    return next(new ErrorResponse('Cart not found', 404));
  }

  // Check if product exists in cart
  const cartItem = cart.items.find(item => item.product.toString() === productId);
  if (!cartItem) {
    return next(new ErrorResponse('Item not found in cart', 404));
  }

  // If quantity > 0, check stock availability
  if (quantity > 0) {
    const product = await Product.findById(productId);
    if (!product || !product.isActive) {
      return next(new ErrorResponse('Product not found', 404));
    }

    if (product.stock.trackInventory && product.stock.quantity < quantity) {
      return next(new ErrorResponse(`Only ${product.stock.quantity} items available in stock`, 400));
    }
  }

  // Update item quantity
  await cart.updateItemQuantity(productId, quantity);

  // Get updated cart with product details
  const updatedCart = await Cart.getCartWithProducts(req.user._id);

  res.status(200).json({
    success: true,
    message: quantity > 0 ? 'Cart item updated successfully' : 'Item removed from cart',
    cart: updatedCart.getSummary(),
    items: updatedCart.items
  });
});

// @desc    Remove item from cart
// @route   DELETE /api/cart/remove/:productId
// @access  Private
const removeFromCart = asyncHandler(async (req, res, next) => {
  const { productId } = req.params;

  // Get cart
  const cart = await Cart.findOne({ user: req.user._id, isActive: true });
  if (!cart) {
    return next(new ErrorResponse('Cart not found', 404));
  }

  // Remove item from cart
  await cart.removeItem(productId);

  // Get updated cart with product details
  const updatedCart = await Cart.getCartWithProducts(req.user._id);

  res.status(200).json({
    success: true,
    message: 'Item removed from cart successfully',
    cart: updatedCart.getSummary(),
    items: updatedCart.items
  });
});

// @desc    Clear cart
// @route   DELETE /api/cart/clear
// @access  Private
const clearCart = asyncHandler(async (req, res, next) => {
  // Get cart
  const cart = await Cart.findOne({ user: req.user._id, isActive: true });
  if (!cart) {
    return next(new ErrorResponse('Cart not found', 404));
  }

  // Clear cart
  await cart.clearCart();

  res.status(200).json({
    success: true,
    message: 'Cart cleared successfully',
    cart: {
      items: [],
      totalItems: 0,
      totalPrice: 0,
      finalPrice: 0,
      appliedCoupons: []
    }
  });
});

// @desc    Apply coupon to cart
// @route   POST /api/cart/coupon/apply
// @access  Private
const applyCoupon = asyncHandler(async (req, res, next) => {
  const { couponCode } = req.body;

  // Get cart
  const cart = await Cart.getCartWithProducts(req.user._id);
  if (!cart || cart.items.length === 0) {
    return next(new ErrorResponse('Cart is empty', 400));
  }

  // Find coupon
  const coupon = await Coupon.findByCode(couponCode);
  if (!coupon) {
    return next(new ErrorResponse('Invalid coupon code', 400));
  }

  // Validate coupon for user
  const validation = coupon.isValidForUser(req.user._id, cart.totalPrice, cart.items);
  if (!validation.valid) {
    return next(new ErrorResponse(validation.message, 400));
  }

  // Calculate discount
  const discountAmount = coupon.calculateDiscount(cart.totalPrice);

  // Apply coupon to cart
  await cart.applyCoupon(couponCode, discountAmount, coupon.discountType);

  // Get updated cart
  const updatedCart = await Cart.getCartWithProducts(req.user._id);

  res.status(200).json({
    success: true,
    message: 'Coupon applied successfully',
    cart: updatedCart.getSummary(),
    items: updatedCart.items,
    discount: discountAmount
  });
});

// @desc    Remove coupon from cart
// @route   DELETE /api/cart/coupon/:couponCode
// @access  Private
const removeCoupon = asyncHandler(async (req, res, next) => {
  const { couponCode } = req.params;

  // Get cart
  const cart = await Cart.findOne({ user: req.user._id, isActive: true });
  if (!cart) {
    return next(new ErrorResponse('Cart not found', 404));
  }

  // Remove coupon from cart
  await cart.removeCoupon(couponCode);

  // Get updated cart with product details
  const updatedCart = await Cart.getCartWithProducts(req.user._id);

  res.status(200).json({
    success: true,
    message: 'Coupon removed successfully',
    cart: updatedCart.getSummary(),
    items: updatedCart.items
  });
});

// @desc    Get cart summary
// @route   GET /api/cart/summary
// @access  Private
const getCartSummary = asyncHandler(async (req, res, next) => {
  const cart = await Cart.findOne({ user: req.user._id, isActive: true });
  
  if (!cart) {
    return res.status(200).json({
      success: true,
      summary: {
        totalItems: 0,
        totalPrice: 0,
        finalPrice: 0,
        itemCount: 0
      }
    });
  }

  res.status(200).json({
    success: true,
    summary: cart.getSummary()
  });
});

// @desc    Validate cart before checkout
// @route   GET /api/cart/validate
// @access  Private
const validateCart = asyncHandler(async (req, res, next) => {
  const cart = await Cart.getCartWithProducts(req.user._id);
  
  if (!cart || cart.items.length === 0) {
    return next(new ErrorResponse('Cart is empty', 400));
  }

  // Validate cart items
  const { validItems, invalidItems } = await cart.validateItems();
  
  if (invalidItems.length > 0) {
    // Remove invalid items and save cart
    cart.items = validItems;
    await cart.save();
    
    return res.status(200).json({
      success: false,
      message: 'Some items in your cart are no longer available',
      validItems,
      invalidItems,
      cart: cart.getSummary()
    });
  }

  res.status(200).json({
    success: true,
    message: 'Cart is valid for checkout',
    cart: cart.getSummary(),
    items: cart.items
  });
});

module.exports = {
  getCart,
  addToCart,
  updateCartItem,
  removeFromCart,
  clearCart,
  applyCoupon,
  removeCoupon,
  getCartSummary,
  validateCart
};
