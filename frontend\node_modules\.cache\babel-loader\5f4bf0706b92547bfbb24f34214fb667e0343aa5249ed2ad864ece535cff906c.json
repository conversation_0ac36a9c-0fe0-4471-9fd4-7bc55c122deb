{"ast": null, "code": "import api from './api';\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', {\n      productId,\n      quantity\n    });\n    return response.data;\n  },\n  // Remove item from cart\n  removeFromCart: async productId => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n  // Apply coupon\n  applyCoupon: async (couponCode, token) => {\n    const response = await api.post('/cart/coupon/apply', {\n      couponCode\n    }, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Remove coupon\n  removeCoupon: async token => {\n    const response = await api.delete('/cart/coupon/remove', {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Validate cart\n  validateCart: async token => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  }\n};\nexport default cartService;", "map": {"version": 3, "names": ["api", "cartService", "getCart", "response", "get", "data", "addToCart", "productId", "quantity", "post", "updateCartItem", "put", "removeFromCart", "delete", "clearCart", "applyCoupon", "couponCode", "token", "headers", "Authorization", "removeCoupon", "validateCart"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/cartService.js"], "sourcesContent": ["import api from './api';\n\nconst cartService = {\n  // Get cart\n  getCart: async () => {\n    const response = await api.get('/cart');\n    return response.data;\n  },\n\n  // Add item to cart\n  addToCart: async (productId, quantity) => {\n    const response = await api.post('/cart/add', { productId, quantity });\n    return response.data;\n  },\n\n  // Update cart item\n  updateCartItem: async (productId, quantity) => {\n    const response = await api.put('/cart/update', { productId, quantity });\n    return response.data;\n  },\n\n  // Remove item from cart\n  removeFromCart: async (productId) => {\n    const response = await api.delete(`/cart/remove/${productId}`);\n    return response.data;\n  },\n\n  // Clear cart\n  clearCart: async () => {\n    const response = await api.delete('/cart/clear');\n    return response.data;\n  },\n\n  // Apply coupon\n  applyCoupon: async (couponCode, token) => {\n    const response = await api.post('/cart/coupon/apply', \n      { couponCode }, \n      {\n        headers: {\n          Authorization: `Bearer ${token}`,\n        },\n      }\n    );\n    return response.data;\n  },\n\n  // Remove coupon\n  removeCoupon: async (token) => {\n    const response = await api.delete('/cart/coupon/remove', {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Validate cart\n  validateCart: async (token) => {\n    const response = await api.post('/cart/validate', {}, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n};\n\nexport default cartService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,MAAMC,QAAQ,GAAG,MAAMH,GAAG,CAACI,GAAG,CAAC,OAAO,CAAC;IACvC,OAAOD,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,QAAQ,KAAK;IACxC,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,WAAW,EAAE;MAAEF,SAAS;MAAEC;IAAS,CAAC,CAAC;IACrE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAK,cAAc,EAAE,MAAAA,CAAOH,SAAS,EAAEC,QAAQ,KAAK;IAC7C,MAAML,QAAQ,GAAG,MAAMH,GAAG,CAACW,GAAG,CAAC,cAAc,EAAE;MAAEJ,SAAS;MAAEC;IAAS,CAAC,CAAC;IACvE,OAAOL,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAO,cAAc,EAAE,MAAOL,SAAS,IAAK;IACnC,MAAMJ,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,gBAAgBN,SAAS,EAAE,CAAC;IAC9D,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,SAAS,EAAE,MAAAA,CAAA,KAAY;IACrB,MAAMX,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,aAAa,CAAC;IAChD,OAAOV,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAU,WAAW,EAAE,MAAAA,CAAOC,UAAU,EAAEC,KAAK,KAAK;IACxC,MAAMd,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,oBAAoB,EAClD;MAAEO;IAAW,CAAC,EACd;MACEE,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUF,KAAK;MAChC;IACF,CACF,CAAC;IACD,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAe,YAAY,EAAE,MAAOH,KAAK,IAAK;IAC7B,MAAMd,QAAQ,GAAG,MAAMH,GAAG,CAACa,MAAM,CAAC,qBAAqB,EAAE;MACvDK,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUF,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAgB,YAAY,EAAE,MAAOJ,KAAK,IAAK;IAC7B,MAAMd,QAAQ,GAAG,MAAMH,GAAG,CAACS,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE;MACpDS,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUF,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOd,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}