#!/bin/bash

echo "========================================"
echo "   BAHUCHAR GROCERIES STORE SETUP"
echo "========================================"
echo

echo "[1/6] Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/"
    exit 1
fi
echo "✓ Node.js is installed ($(node --version))"

echo
echo "[2/6] Installing backend dependencies..."
cd backend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install backend dependencies"
    exit 1
fi
echo "✓ Backend dependencies installed"

echo
echo "[3/6] Installing frontend dependencies..."
cd ../frontend
npm install
if [ $? -ne 0 ]; then
    echo "ERROR: Failed to install frontend dependencies"
    exit 1
fi
echo "✓ Frontend dependencies installed"

echo
echo "[4/6] Setting up database..."
cd ../backend
npm run seed
if [ $? -ne 0 ]; then
    echo "WARNING: Database seeding failed. You may need to start MongoDB first."
fi
echo "✓ Database setup attempted"

echo
echo "[5/6] Creating admin user..."
npm run create-admin
if [ $? -ne 0 ]; then
    echo "WARNING: Admin user creation failed. You may need to start MongoDB first."
fi
echo "✓ Admin user setup attempted"

echo
echo "[6/6] Updating product images..."
npm run update-images
if [ $? -ne 0 ]; then
    echo "WARNING: Image update failed. You may need to start MongoDB first."
fi
echo "✓ Product images updated"

echo
echo "========================================"
echo "          SETUP COMPLETE!"
echo "========================================"
echo
echo "To run the application:"
echo "1. Start MongoDB (if not already running)"
echo "2. Open two terminals:"
echo "   Terminal 1: cd backend && npm start"
echo "   Terminal 2: cd frontend && npm start"
echo
echo "3. Open http://localhost:3000 in your browser"
echo
echo "Admin Login:"
echo "Email: <EMAIL>"
echo "Password: admin123"
echo
echo "========================================"
