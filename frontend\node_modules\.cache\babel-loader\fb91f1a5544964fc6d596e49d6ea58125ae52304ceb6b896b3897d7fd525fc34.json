{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECT\\\\Groceries store applicaion\\\\frontend\\\\src\\\\pages\\\\admin\\\\AdminDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { FiUsers, FiShoppingBag, FiPackage, FiDollarSign, FiTrendingUp, FiEye, FiEdit, FiTrash2, FiPlus } from 'react-icons/fi';\nimport { formatPrice } from '../../utils/currency';\nimport productService from '../../services/productService';\nimport orderService from '../../services/orderService';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user,\n    token\n  } = useSelector(state => state.auth);\n  const navigate = useNavigate();\n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalOrders: 0,\n    totalUsers: 0,\n    totalRevenue: 0\n  });\n  const [recentProducts, setRecentProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n  useEffect(() => {\n    if (!user || user.role !== 'admin') {\n      navigate('/');\n      return;\n    }\n    fetchDashboardData();\n  }, [user, navigate, token]);\n  const fetchDashboardData = async () => {\n    try {\n      var _ordersResponse$order;\n      setLoading(true);\n\n      // Fetch products\n      const productsResponse = await productService.getProducts({\n        limit: 5\n      });\n      setRecentProducts(productsResponse.products || []);\n\n      // Fetch orders\n      const ordersResponse = await orderService.getOrders({\n        limit: 5\n      }, token);\n      setRecentOrders(ordersResponse.orders || []);\n\n      // Calculate stats\n      setStats({\n        totalProducts: productsResponse.total || 0,\n        totalOrders: ordersResponse.total || 0,\n        totalUsers: 150,\n        // Mock data\n        totalRevenue: ((_ordersResponse$order = ordersResponse.orders) === null || _ordersResponse$order === void 0 ? void 0 : _ordersResponse$order.reduce((sum, order) => sum + order.totalAmount, 0)) || 0\n      });\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-4 text-gray-600\",\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-3xl font-bold text-gray-900\",\n          children: \"Admin Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: [\"Welcome back, \", user.firstName, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-blue-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(FiPackage, {\n                className: \"w-6 h-6 text-blue-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stats.totalProducts\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-green-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(FiShoppingBag, {\n                className: \"w-6 h-6 text-green-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stats.totalOrders\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-purple-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(FiUsers, {\n                className: \"w-6 h-6 text-purple-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Users\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stats.totalUsers\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 bg-yellow-100 rounded-full\",\n              children: /*#__PURE__*/_jsxDEV(FiDollarSign, {\n                className: \"w-6 h-6 text-yellow-600\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: \"Total Revenue\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: formatPrice(stats.totalRevenue)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Recent Products\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/admin/products'),\n                className: \"text-green-600 hover:text-green-700 text-sm font-medium\",\n                children: \"View All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: recentProducts.map(product => {\n                var _product$images, _product$images$;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: ((_product$images = product.images) === null || _product$images === void 0 ? void 0 : (_product$images$ = _product$images[0]) === null || _product$images$ === void 0 ? void 0 : _product$images$.url) || '/api/placeholder/50/50',\n                    alt: product.name,\n                    className: \"w-12 h-12 object-cover rounded-lg\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: formatPrice(product.price)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"p-1 text-gray-400 hover:text-blue-600\",\n                      children: /*#__PURE__*/_jsxDEV(FiEye, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 175,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 174,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"p-1 text-gray-400 hover:text-green-600\",\n                      children: /*#__PURE__*/_jsxDEV(FiEdit, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 178,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 177,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"p-1 text-gray-400 hover:text-red-600\",\n                      children: /*#__PURE__*/_jsxDEV(FiTrash2, {\n                        className: \"w-4 h-4\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 181,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 180,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 173,\n                    columnNumber: 21\n                  }, this)]\n                }, product._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 border-b border-gray-200\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold text-gray-900\",\n                children: \"Recent Orders\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/admin/orders'),\n                className: \"text-green-600 hover:text-green-700 text-sm font-medium\",\n                children: \"View All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: recentOrders.map(order => {\n                var _order$user, _order$user2;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [\"Order #\", order.orderNumber]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-500\",\n                      children: [(_order$user = order.user) === null || _order$user === void 0 ? void 0 : _order$user.firstName, \" \", (_order$user2 = order.user) === null || _order$user2 === void 0 ? void 0 : _order$user2.lastName]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 211,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 207,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-right\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: formatPrice(order.totalAmount)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 216,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${order.orderStatus === 'delivered' ? 'bg-green-100 text-green-800' : order.orderStatus === 'processing' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                      children: order.orderStatus\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 21\n                  }, this)]\n                }, order._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900 mb-4\",\n          children: \"Quick Actions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/products/new'),\n            className: \"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(FiPlus, {\n              className: \"w-5 h-5 text-green-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Add New Product\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/orders'),\n            className: \"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(FiShoppingBag, {\n              className: \"w-5 h-5 text-blue-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-900\",\n              children: \"Manage Orders\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/admin/analytics'),\n            className: \"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\",\n            children: [/*#__PURE__*/_jsxDEV(FiTrendingUp, {\n              className: \"w-5 h-5 text-purple-600 mr-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-gray-900\",\n              children: \"View Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"EpMUOj4eEyAWAtAynvjoUeufY+s=\", false, function () {\n  return [useSelector, useNavigate];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useSelector", "useNavigate", "FiUsers", "FiShoppingBag", "FiPackage", "FiDollarSign", "FiTrendingUp", "FiEye", "FiEdit", "FiTrash2", "FiPlus", "formatPrice", "productService", "orderService", "toast", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "token", "state", "auth", "navigate", "loading", "setLoading", "stats", "setStats", "totalProducts", "totalOrders", "totalUsers", "totalRevenue", "recentProducts", "setRecentProducts", "recentOrders", "setRecentOrders", "role", "fetchDashboardData", "_ordersResponse$order", "productsResponse", "getProducts", "limit", "products", "ordersResponse", "getOrders", "orders", "total", "reduce", "sum", "order", "totalAmount", "error", "console", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "firstName", "onClick", "map", "product", "_product$images", "_product$images$", "src", "images", "url", "alt", "name", "price", "_id", "_order$user", "_order$user2", "orderNumber", "lastName", "orderStatus", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/pages/admin/AdminDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useSelector } from 'react-redux';\nimport { useNavigate } from 'react-router-dom';\nimport { \n  FiUsers, \n  FiShoppingBag, \n  FiPackage, \n  FiDollarSign,\n  FiTrendingUp,\n  FiEye,\n  FiEdit,\n  FiTrash2,\n  FiPlus\n} from 'react-icons/fi';\nimport { formatPrice } from '../../utils/currency';\nimport productService from '../../services/productService';\nimport orderService from '../../services/orderService';\nimport toast from 'react-hot-toast';\n\nconst AdminDashboard = () => {\n  const { user, token } = useSelector((state) => state.auth);\n  const navigate = useNavigate();\n  \n  const [loading, setLoading] = useState(true);\n  const [stats, setStats] = useState({\n    totalProducts: 0,\n    totalOrders: 0,\n    totalUsers: 0,\n    totalRevenue: 0\n  });\n  const [recentProducts, setRecentProducts] = useState([]);\n  const [recentOrders, setRecentOrders] = useState([]);\n\n  useEffect(() => {\n    if (!user || user.role !== 'admin') {\n      navigate('/');\n      return;\n    }\n    \n    fetchDashboardData();\n  }, [user, navigate, token]);\n\n  const fetchDashboardData = async () => {\n    try {\n      setLoading(true);\n      \n      // Fetch products\n      const productsResponse = await productService.getProducts({ limit: 5 });\n      setRecentProducts(productsResponse.products || []);\n      \n      // Fetch orders\n      const ordersResponse = await orderService.getOrders({ limit: 5 }, token);\n      setRecentOrders(ordersResponse.orders || []);\n      \n      // Calculate stats\n      setStats({\n        totalProducts: productsResponse.total || 0,\n        totalOrders: ordersResponse.total || 0,\n        totalUsers: 150, // Mock data\n        totalRevenue: ordersResponse.orders?.reduce((sum, order) => sum + order.totalAmount, 0) || 0\n      });\n      \n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n      toast.error('Failed to load dashboard data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!user || user.role !== 'admin') {\n    return null;\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">Admin Dashboard</h1>\n          <p className=\"text-gray-600\">Welcome back, {user.firstName}!</p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-blue-100 rounded-full\">\n                <FiPackage className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Products</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalProducts}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-green-100 rounded-full\">\n                <FiShoppingBag className=\"w-6 h-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Orders</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalOrders}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-purple-100 rounded-full\">\n                <FiUsers className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{stats.totalUsers}</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-3 bg-yellow-100 rounded-full\">\n                <FiDollarSign className=\"w-6 h-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Revenue</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{formatPrice(stats.totalRevenue)}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Recent Products */}\n          <div className=\"bg-white rounded-lg shadow-md\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Recent Products</h2>\n                <button\n                  onClick={() => navigate('/admin/products')}\n                  className=\"text-green-600 hover:text-green-700 text-sm font-medium\"\n                >\n                  View All\n                </button>\n              </div>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {recentProducts.map((product) => (\n                  <div key={product._id} className=\"flex items-center space-x-4\">\n                    <img\n                      src={product.images?.[0]?.url || '/api/placeholder/50/50'}\n                      alt={product.name}\n                      className=\"w-12 h-12 object-cover rounded-lg\"\n                    />\n                    <div className=\"flex-1\">\n                      <h3 className=\"text-sm font-medium text-gray-900\">{product.name}</h3>\n                      <p className=\"text-sm text-gray-500\">{formatPrice(product.price)}</p>\n                    </div>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"p-1 text-gray-400 hover:text-blue-600\">\n                        <FiEye className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"p-1 text-gray-400 hover:text-green-600\">\n                        <FiEdit className=\"w-4 h-4\" />\n                      </button>\n                      <button className=\"p-1 text-gray-400 hover:text-red-600\">\n                        <FiTrash2 className=\"w-4 h-4\" />\n                      </button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Orders */}\n          <div className=\"bg-white rounded-lg shadow-md\">\n            <div className=\"p-6 border-b border-gray-200\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Recent Orders</h2>\n                <button\n                  onClick={() => navigate('/admin/orders')}\n                  className=\"text-green-600 hover:text-green-700 text-sm font-medium\"\n                >\n                  View All\n                </button>\n              </div>\n            </div>\n            <div className=\"p-6\">\n              <div className=\"space-y-4\">\n                {recentOrders.map((order) => (\n                  <div key={order._id} className=\"flex items-center justify-between\">\n                    <div>\n                      <h3 className=\"text-sm font-medium text-gray-900\">\n                        Order #{order.orderNumber}\n                      </h3>\n                      <p className=\"text-sm text-gray-500\">\n                        {order.user?.firstName} {order.user?.lastName}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-sm font-medium text-gray-900\">\n                        {formatPrice(order.totalAmount)}\n                      </p>\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        order.orderStatus === 'delivered' \n                          ? 'bg-green-100 text-green-800'\n                          : order.orderStatus === 'processing'\n                          ? 'bg-yellow-100 text-yellow-800'\n                          : 'bg-gray-100 text-gray-800'\n                      }`}>\n                        {order.orderStatus}\n                      </span>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mt-8\">\n          <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <button\n              onClick={() => navigate('/admin/products/new')}\n              className=\"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n            >\n              <FiPlus className=\"w-5 h-5 text-green-600 mr-2\" />\n              <span className=\"font-medium text-gray-900\">Add New Product</span>\n            </button>\n            \n            <button\n              onClick={() => navigate('/admin/orders')}\n              className=\"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n            >\n              <FiShoppingBag className=\"w-5 h-5 text-blue-600 mr-2\" />\n              <span className=\"font-medium text-gray-900\">Manage Orders</span>\n            </button>\n            \n            <button\n              onClick={() => navigate('/admin/analytics')}\n              className=\"flex items-center justify-center p-4 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow\"\n            >\n              <FiTrendingUp className=\"w-5 h-5 text-purple-600 mr-2\" />\n              <span className=\"font-medium text-gray-900\">View Analytics</span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,OAAO,EACPC,aAAa,EACbC,SAAS,EACTC,YAAY,EACZC,YAAY,EACZC,KAAK,EACLC,MAAM,EACNC,QAAQ,EACRC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,sBAAsB;AAClD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,YAAY,MAAM,6BAA6B;AACtD,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAM,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC;EAC1D,MAAMC,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC;IACjC8B,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnC,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAC,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACoB,IAAI,IAAIA,IAAI,CAACiB,IAAI,KAAK,OAAO,EAAE;MAClCb,QAAQ,CAAC,GAAG,CAAC;MACb;IACF;IAEAc,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAAClB,IAAI,EAAEI,QAAQ,EAAEH,KAAK,CAAC,CAAC;EAE3B,MAAMiB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MAAA,IAAAC,qBAAA;MACFb,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMc,gBAAgB,GAAG,MAAM3B,cAAc,CAAC4B,WAAW,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC,CAAC;MACvER,iBAAiB,CAACM,gBAAgB,CAACG,QAAQ,IAAI,EAAE,CAAC;;MAElD;MACA,MAAMC,cAAc,GAAG,MAAM9B,YAAY,CAAC+B,SAAS,CAAC;QAAEH,KAAK,EAAE;MAAE,CAAC,EAAErB,KAAK,CAAC;MACxEe,eAAe,CAACQ,cAAc,CAACE,MAAM,IAAI,EAAE,CAAC;;MAE5C;MACAlB,QAAQ,CAAC;QACPC,aAAa,EAAEW,gBAAgB,CAACO,KAAK,IAAI,CAAC;QAC1CjB,WAAW,EAAEc,cAAc,CAACG,KAAK,IAAI,CAAC;QACtChB,UAAU,EAAE,GAAG;QAAE;QACjBC,YAAY,EAAE,EAAAO,qBAAA,GAAAK,cAAc,CAACE,MAAM,cAAAP,qBAAA,uBAArBA,qBAAA,CAAuBS,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,CAACC,WAAW,EAAE,CAAC,CAAC,KAAI;MAC7F,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDrC,KAAK,CAACqC,KAAK,CAAC,+BAA+B,CAAC;IAC9C,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACiB,IAAI,KAAK,OAAO,EAAE;IAClC,OAAO,IAAI;EACb;EAEA,IAAIZ,OAAO,EAAE;IACX,oBACER,OAAA;MAAKqC,SAAS,EAAC,0DAA0D;MAAAC,QAAA,eACvEtC,OAAA;QAAKqC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtC,OAAA;UAAKqC,SAAS,EAAC;QAAyE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/F1C,OAAA;UAAGqC,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1C,OAAA;IAAKqC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,eACtCtC,OAAA;MAAKqC,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DtC,OAAA;QAAKqC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBtC,OAAA;UAAIqC,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrE1C,OAAA;UAAGqC,SAAS,EAAC,eAAe;UAAAC,QAAA,GAAC,gBAAc,EAACnC,IAAI,CAACwC,SAAS,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxEtC,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDtC,OAAA;YAAKqC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtC,OAAA;cAAKqC,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CtC,OAAA,CAACZ,SAAS;gBAACiD,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtC,OAAA;gBAAGqC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACnE1C,OAAA;gBAAGqC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE5B,KAAK,CAACE;cAAa;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDtC,OAAA;YAAKqC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtC,OAAA;cAAKqC,SAAS,EAAC,+BAA+B;cAAAC,QAAA,eAC5CtC,OAAA,CAACb,aAAa;gBAACkD,SAAS,EAAC;cAAwB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtC,OAAA;gBAAGqC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjE1C,OAAA;gBAAGqC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE5B,KAAK,CAACG;cAAW;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDtC,OAAA;YAAKqC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtC,OAAA;cAAKqC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7CtC,OAAA,CAACd,OAAO;gBAACmD,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtC,OAAA;gBAAGqC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChE1C,OAAA;gBAAGqC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE5B,KAAK,CAACI;cAAU;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1C,OAAA;UAAKqC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDtC,OAAA;YAAKqC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCtC,OAAA;cAAKqC,SAAS,EAAC,gCAAgC;cAAAC,QAAA,eAC7CtC,OAAA,CAACX,YAAY;gBAACgD,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC,eACN1C,OAAA;cAAKqC,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBtC,OAAA;gBAAGqC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAClE1C,OAAA;gBAAGqC,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAE3C,WAAW,CAACe,KAAK,CAACK,YAAY;cAAC;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN1C,OAAA;QAAKqC,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBAEpDtC,OAAA;UAAKqC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CtC,OAAA;YAAKqC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CtC,OAAA;cAAKqC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtC,OAAA;gBAAIqC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxE1C,OAAA;gBACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,iBAAiB,CAAE;gBAC3C8B,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EACpE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClBtC,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBtB,cAAc,CAAC6B,GAAG,CAAEC,OAAO;gBAAA,IAAAC,eAAA,EAAAC,gBAAA;gBAAA,oBAC1BhD,OAAA;kBAAuBqC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC5DtC,OAAA;oBACEiD,GAAG,EAAE,EAAAF,eAAA,GAAAD,OAAO,CAACI,MAAM,cAAAH,eAAA,wBAAAC,gBAAA,GAAdD,eAAA,CAAiB,CAAC,CAAC,cAAAC,gBAAA,uBAAnBA,gBAAA,CAAqBG,GAAG,KAAI,wBAAyB;oBAC1DC,GAAG,EAAEN,OAAO,CAACO,IAAK;oBAClBhB,SAAS,EAAC;kBAAmC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACF1C,OAAA;oBAAKqC,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEQ,OAAO,CAACO;oBAAI;sBAAAd,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrE1C,OAAA;sBAAGqC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAE3C,WAAW,CAACmD,OAAO,CAACQ,KAAK;oBAAC;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClE,CAAC,eACN1C,OAAA;oBAAKqC,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAC7BtC,OAAA;sBAAQqC,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,eACvDtC,OAAA,CAACT,KAAK;wBAAC8C,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,eACT1C,OAAA;sBAAQqC,SAAS,EAAC,wCAAwC;sBAAAC,QAAA,eACxDtC,OAAA,CAACR,MAAM;wBAAC6C,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACT1C,OAAA;sBAAQqC,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,eACtDtC,OAAA,CAACP,QAAQ;wBAAC4C,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GApBEI,OAAO,CAACS,GAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAqBhB,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1C,OAAA;UAAKqC,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5CtC,OAAA;YAAKqC,SAAS,EAAC,8BAA8B;YAAAC,QAAA,eAC3CtC,OAAA;cAAKqC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtC,OAAA;gBAAIqC,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtE1C,OAAA;gBACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,eAAe,CAAE;gBACzC8B,SAAS,EAAC,yDAAyD;gBAAAC,QAAA,EACpE;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1C,OAAA;YAAKqC,SAAS,EAAC,KAAK;YAAAC,QAAA,eAClBtC,OAAA;cAAKqC,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBpB,YAAY,CAAC2B,GAAG,CAAEZ,KAAK;gBAAA,IAAAuB,WAAA,EAAAC,YAAA;gBAAA,oBACtBzD,OAAA;kBAAqBqC,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,gBAChEtC,OAAA;oBAAAsC,QAAA,gBACEtC,OAAA;sBAAIqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,SACzC,EAACL,KAAK,CAACyB,WAAW;oBAAA;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB,CAAC,eACL1C,OAAA;sBAAGqC,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,IAAAkB,WAAA,GACjCvB,KAAK,CAAC9B,IAAI,cAAAqD,WAAA,uBAAVA,WAAA,CAAYb,SAAS,EAAC,GAAC,GAAAc,YAAA,GAACxB,KAAK,CAAC9B,IAAI,cAAAsD,YAAA,uBAAVA,YAAA,CAAYE,QAAQ;oBAAA;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACN1C,OAAA;oBAAKqC,SAAS,EAAC,YAAY;oBAAAC,QAAA,gBACzBtC,OAAA;sBAAGqC,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAC7C3C,WAAW,CAACsC,KAAK,CAACC,WAAW;oBAAC;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9B,CAAC,eACJ1C,OAAA;sBAAMqC,SAAS,EAAE,4DACfJ,KAAK,CAAC2B,WAAW,KAAK,WAAW,GAC7B,6BAA6B,GAC7B3B,KAAK,CAAC2B,WAAW,KAAK,YAAY,GAClC,+BAA+B,GAC/B,2BAA2B,EAC9B;sBAAAtB,QAAA,EACAL,KAAK,CAAC2B;oBAAW;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAtBET,KAAK,CAACsB,GAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAuBd,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKqC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBtC,OAAA;UAAIqC,SAAS,EAAC,0CAA0C;UAAAC,QAAA,EAAC;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3E1C,OAAA;UAAKqC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDtC,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,qBAAqB,CAAE;YAC/C8B,SAAS,EAAC,sGAAsG;YAAAC,QAAA,gBAEhHtC,OAAA,CAACN,MAAM;cAAC2C,SAAS,EAAC;YAA6B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAClD1C,OAAA;cAAMqC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAET1C,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,eAAe,CAAE;YACzC8B,SAAS,EAAC,sGAAsG;YAAAC,QAAA,gBAEhHtC,OAAA,CAACb,aAAa;cAACkD,SAAS,EAAC;YAA4B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD1C,OAAA;cAAMqC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAET1C,OAAA;YACE4C,OAAO,EAAEA,CAAA,KAAMrC,QAAQ,CAAC,kBAAkB,CAAE;YAC5C8B,SAAS,EAAC,sGAAsG;YAAAC,QAAA,gBAEhHtC,OAAA,CAACV,YAAY;cAAC+C,SAAS,EAAC;YAA8B;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACzD1C,OAAA;cAAMqC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxC,EAAA,CAxPID,cAAc;EAAA,QACMjB,WAAW,EAClBC,WAAW;AAAA;AAAA4E,EAAA,GAFxB5D,cAAc;AA0PpB,eAAeA,cAAc;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}