{"ast": null, "code": "import api from './api';\nconst productService = {\n  // Get all products\n  getProducts: async (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products?${queryString}`);\n    return response.data;\n  },\n  // Get single product\n  getProduct: async id => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n  // Get featured products\n  getFeaturedProducts: async () => {\n    console.log('ProductService: Making API call to /products/featured');\n    const response = await api.get('/products/featured');\n    console.log('ProductService: API response:', response.data);\n    return response.data;\n  },\n  // Search products\n  searchProducts: async searchParams => {\n    const queryString = new URLSearchParams(searchParams).toString();\n    const response = await api.get(`/products/search?${queryString}`);\n    return response.data;\n  },\n  // Get related products\n  getRelatedProducts: async id => {\n    const response = await api.get(`/products/${id}/related`);\n    return response.data;\n  },\n  // Get products by category\n  getProductsByCategory: async (categoryId, params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products/category/${categoryId}?${queryString}`);\n    return response.data;\n  },\n  // Add product review\n  addReview: async (productId, reviewData, token) => {\n    const response = await api.post(`/products/${productId}/reviews`, reviewData, {\n      headers: {\n        Authorization: `Bearer ${token}`\n      }\n    });\n    return response.data;\n  },\n  // Get product reviews\n  getReviews: async (productId, params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products/${productId}/reviews?${queryString}`);\n    return response.data;\n  }\n};\nexport default productService;", "map": {"version": 3, "names": ["api", "productService", "getProducts", "params", "queryString", "URLSearchParams", "toString", "response", "get", "data", "getProduct", "id", "getFeaturedProducts", "console", "log", "searchProducts", "searchParams", "getRelatedProducts", "getProductsByCategory", "categoryId", "add<PERSON>eview", "productId", "reviewData", "token", "post", "headers", "Authorization", "getReviews"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/PROJECT/Groceries store applicaion/frontend/src/services/productService.js"], "sourcesContent": ["import api from './api';\n\nconst productService = {\n  // Get all products\n  getProducts: async (params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products?${queryString}`);\n    return response.data;\n  },\n\n  // Get single product\n  getProduct: async (id) => {\n    const response = await api.get(`/products/${id}`);\n    return response.data;\n  },\n\n  // Get featured products\n  getFeaturedProducts: async () => {\n    console.log('ProductService: Making API call to /products/featured');\n    const response = await api.get('/products/featured');\n    console.log('ProductService: API response:', response.data);\n    return response.data;\n  },\n\n  // Search products\n  searchProducts: async (searchParams) => {\n    const queryString = new URLSearchParams(searchParams).toString();\n    const response = await api.get(`/products/search?${queryString}`);\n    return response.data;\n  },\n\n  // Get related products\n  getRelatedProducts: async (id) => {\n    const response = await api.get(`/products/${id}/related`);\n    return response.data;\n  },\n\n  // Get products by category\n  getProductsByCategory: async (categoryId, params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products/category/${categoryId}?${queryString}`);\n    return response.data;\n  },\n\n  // Add product review\n  addReview: async (productId, reviewData, token) => {\n    const response = await api.post(`/products/${productId}/reviews`, reviewData, {\n      headers: {\n        Authorization: `Bearer ${token}`,\n      },\n    });\n    return response.data;\n  },\n\n  // Get product reviews\n  getReviews: async (productId, params = {}) => {\n    const queryString = new URLSearchParams(params).toString();\n    const response = await api.get(`/products/${productId}/reviews?${queryString}`);\n    return response.data;\n  },\n};\n\nexport default productService;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AAEvB,MAAMC,cAAc,GAAG;EACrB;EACAC,WAAW,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAClC,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,MAAMC,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,aAAaJ,WAAW,EAAE,CAAC;IAC1D,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAC,UAAU,EAAE,MAAOC,EAAE,IAAK;IACxB,MAAMJ,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,aAAaG,EAAE,EAAE,CAAC;IACjD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAG,mBAAmB,EAAE,MAAAA,CAAA,KAAY;IAC/BC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;IACpE,MAAMP,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,oBAAoB,CAAC;IACpDK,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEP,QAAQ,CAACE,IAAI,CAAC;IAC3D,OAAOF,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAM,cAAc,EAAE,MAAOC,YAAY,IAAK;IACtC,MAAMZ,WAAW,GAAG,IAAIC,eAAe,CAACW,YAAY,CAAC,CAACV,QAAQ,CAAC,CAAC;IAChE,MAAMC,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,oBAAoBJ,WAAW,EAAE,CAAC;IACjE,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAQ,kBAAkB,EAAE,MAAON,EAAE,IAAK;IAChC,MAAMJ,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,aAAaG,EAAE,UAAU,CAAC;IACzD,OAAOJ,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAS,qBAAqB,EAAE,MAAAA,CAAOC,UAAU,EAAEhB,MAAM,GAAG,CAAC,CAAC,KAAK;IACxD,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,MAAMC,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,sBAAsBW,UAAU,IAAIf,WAAW,EAAE,CAAC;IACjF,OAAOG,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAW,SAAS,EAAE,MAAAA,CAAOC,SAAS,EAAEC,UAAU,EAAEC,KAAK,KAAK;IACjD,MAAMhB,QAAQ,GAAG,MAAMP,GAAG,CAACwB,IAAI,CAAC,aAAaH,SAAS,UAAU,EAAEC,UAAU,EAAE;MAC5EG,OAAO,EAAE;QACPC,aAAa,EAAE,UAAUH,KAAK;MAChC;IACF,CAAC,CAAC;IACF,OAAOhB,QAAQ,CAACE,IAAI;EACtB,CAAC;EAED;EACAkB,UAAU,EAAE,MAAAA,CAAON,SAAS,EAAElB,MAAM,GAAG,CAAC,CAAC,KAAK;IAC5C,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;IAC1D,MAAMC,QAAQ,GAAG,MAAMP,GAAG,CAACQ,GAAG,CAAC,aAAaa,SAAS,YAAYjB,WAAW,EAAE,CAAC;IAC/E,OAAOG,QAAQ,CAACE,IAAI;EACtB;AACF,CAAC;AAED,eAAeR,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}