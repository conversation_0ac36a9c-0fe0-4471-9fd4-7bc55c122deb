"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var _LinePrependor;

module.exports = _LinePrependor = /*#__PURE__*/function () {
  function _LinePrependor(_config) {
    _classCallCheck(this, _LinePrependor);

    this._config = _config;
    this._lineNo = -1;
  }

  _createClass(_LinePrependor, [{
    key: "render",
    value: function render(inherited, options) {
      this._lineNo++;
      return '<none>' + this._render(inherited, options) + '</none>';
    }
  }]);

  return _LinePrependor;
}();